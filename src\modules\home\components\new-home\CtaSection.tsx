'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useRouter } from '@/libs/i18nNavigation'
import { gsap } from 'gsap'
import React, { useEffect, useRef } from 'react'
import { FaArrowRight, FaCheck } from 'react-icons/fa'

// Benefits list
const benefits = [
  'Tạo trang đặt chỗ chuyên nghiệp',
  'Quản lý lịch trình dễ dàng',
  'Nhận thông báo đặt chỗ tự động',
  'Tích hợp thanh toán trực tuyến',
  'Báo cáo và phân tích chi tiết',
  'Hỗ trợ kỹ thuật 24/7',
]

export const CtaSection = () => {
  const router = useRouter()
  const sectionRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const benefitsRef = useRef<HTMLUListElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Create a timeline for the CTA section
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 70%',
        },
      })

      // Animate the content
      tl.from(contentRef.current, {
        y: 50,
        opacity: 0,
        duration: 0.8,
      })
        .from(
          '.benefit-item',
          {
            x: -30,
            opacity: 0,
            stagger: 0.1,
            duration: 0.5,
          },
          '-=0.4',
        )
        .from(
          ctaRef.current,
          {
            y: 30,
            opacity: 0,
            duration: 0.6,
          },
          '-=0.2',
        )

      // Create a pulsing animation for the CTA button
      gsap.to('.cta-button', {
        boxShadow: '0 0 0 8px rgba(249, 115, 22, 0.2)',
        repeat: -1,
        yoyo: true,
        duration: 1.5,
      })

      // Create a floating animation for the decorative elements
      gsap.to('.floating-shape', {
        y: '20px',
        rotation: 15,
        duration: 3,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        stagger: {
          each: 0.5,
          from: 'random',
        },
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  const handleGetStarted = () => {
    router.push('/auth/signup')
  }

  return (
    <section
      ref={sectionRef}
      className="py-24 bg-gradient-to-br from-orange-500 to-orange-600 text-white relative overflow-hidden"
    >
      {/* Decorative shapes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="floating-shape absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full" />
        <div className="floating-shape absolute top-1/3 right-1/4 w-32 h-32 bg-white opacity-5 rounded-full" />
        <div className="floating-shape absolute bottom-1/4 left-1/3 w-24 h-24 bg-white opacity-10 rounded-full" />
        <div className="floating-shape absolute bottom-10 right-10 w-16 h-16 bg-white opacity-5 rounded-full" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          <div ref={contentRef} className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Sẵn sàng tạo trang đặt chỗ của riêng bạn?
            </h2>
            <p className="text-xl text-orange-100 mb-8">
              Bắt đầu miễn phí ngay hôm nay và nâng cấp khi doanh nghiệp của bạn phát triển
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <ul ref={benefitsRef} className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <li key={index} className="benefit-item flex items-center gap-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                        <FaCheck className="text-xs" />
                      </span>
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div ref={ctaRef} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-2xl font-bold mb-4">Bắt đầu miễn phí</h3>
                <p className="text-orange-100 mb-6">
                  Không cần thẻ tín dụng. Nâng cấp hoặc hủy bất cứ lúc nào.
                </p>

                <Button
                  onClick={handleGetStarted}
                  className="cta-button w-full bg-white text-orange-600 hover:bg-orange-50 text-lg py-6 rounded-full font-medium shadow-lg transition-all duration-300 group"
                >
                  Tạo tài khoản miễn phí
                  <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>

                <p className="text-sm text-center mt-4 text-orange-100">
                  Đã có hơn 10,000+ người dùng tin tưởng PickSlot
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
