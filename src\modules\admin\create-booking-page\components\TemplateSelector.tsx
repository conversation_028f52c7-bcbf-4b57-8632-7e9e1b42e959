import type { BookingPageTemplate } from '../constants/templates'
import React, { memo } from 'react'
import { TemplateCategory } from '../constants/templates'

type TemplateSelectorProps = {
  templates: BookingPageTemplate[]
  selected: string
  onSelect: (id: string) => void
  onCategoryFilter?: (category: TemplateCategory | 'all') => void
}

const TemplateSelector = memo(({ templates, selected, onSelect, onCategoryFilter }: TemplateSelectorProps) => {
  const [search, setSearch] = React.useState('')
  const [activeCategory, setActiveCategory] = React.useState<TemplateCategory | 'all'>('all')

  // Filter templates by search term and category
  const filtered = templates.filter(
    (tpl) => {
      const matchesSearch
        = tpl.name.toLowerCase().includes(search.toLowerCase())
          || tpl.description.toLowerCase().includes(search.toLowerCase())
          || tpl.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()))

      const matchesCategory = activeCategory === 'all' || tpl.category === activeCategory

      return matchesSearch && matchesCategory
    },
  )

  // Handle category change
  const handleCategoryChange = (category: TemplateCategory | 'all') => {
    setActiveCategory(category)
    if (onCategoryFilter) {
      onCategoryFilter(category)
    }
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="font-semibold text-gray-700 text-lg truncate">Chọn mẫu giao diện</div>

        {/* Search input with icon */}
        <div className="relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
          <input
            type="text"
            placeholder="Tìm kiếm mẫu..."
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="w-full pl-10 pr-3 py-1.5 border rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-300 bg-gray-50"
          />
        </div>
      </div>

      {/* Category tabs - simplified */}
      <div className="flex border-b mb-3">
        <button
          type="button"
          onClick={() => handleCategoryChange('all')}
          className={`px-4 py-2 text-sm transition border-b-2 -mb-px ${
            activeCategory === 'all'
              ? 'border-blue-500 text-blue-700 font-medium'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Tất cả
        </button>
        <button
          type="button"
          onClick={() => handleCategoryChange(TemplateCategory.EVENT)}
          className={`px-4 py-2 text-sm transition border-b-2 -mb-px ${
            activeCategory === TemplateCategory.EVENT
              ? 'border-blue-500 text-blue-700 font-medium'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Sự kiện
        </button>
        <button
          type="button"
          onClick={() => handleCategoryChange(TemplateCategory.SPORT)}
          className={`px-4 py-2 text-sm transition border-b-2 -mb-px ${
            activeCategory === TemplateCategory.SPORT
              ? 'border-green-500 text-green-700 font-medium'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Thể thao
        </button>
        <button
          type="button"
          onClick={() => handleCategoryChange(TemplateCategory.SERVICE)}
          className={`px-4 py-2 text-sm transition border-b-2 -mb-px ${
            activeCategory === TemplateCategory.SERVICE
              ? 'border-purple-500 text-purple-700 font-medium'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Dịch vụ
        </button>
        <button
          type="button"
          onClick={() => handleCategoryChange(TemplateCategory.TRANSPORTATION)}
          className={`px-4 py-2 text-sm transition border-b-2 -mb-px ${
            activeCategory === TemplateCategory.TRANSPORTATION
              ? 'border-red-500 text-red-700 font-medium'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          Vận chuyển
        </button>
      </div>

      {/* Template list - vertical layout with dynamic height to match "Thông tin cơ bản" */}
      <div className="h-[calc(100vh-20rem)] min-h-[200px] max-h-[300px] overflow-y-auto rounded-lg bg-gray-50 custom-scrollbar">
        {filtered.length === 0 && (
          <div className="p-8 text-center text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p>Không tìm thấy mẫu phù hợp</p>
          </div>
        )}
        <div className="divide-y divide-gray-100">
          {filtered.map((tpl) => {
            const isSelected = selected === tpl.id
            return (
              <button
                key={tpl.id}
                type="button"
                className={`w-full flex items-center gap-4 px-4 py-3 text-left transition hover:bg-blue-50 focus:bg-blue-100 ${
                  isSelected
                    ? 'bg-blue-50 border-l-4 border-blue-500'
                    : 'border-l-4 border-transparent'
                } relative group`}
                onClick={() => onSelect(tpl.id)}
                tabIndex={0}
                aria-label={`Chọn template ${tpl.name}`}
              >
                <div className="relative flex-shrink-0 w-20 h-20 rounded overflow-hidden border bg-gray-50 group-hover:border-blue-300">
                  <img src={tpl.image} alt={tpl.name} className="object-cover w-full h-full" />
                  {isSelected && (
                    <div className="absolute top-0 right-0 bg-blue-500 text-white rounded-bl-md p-1 shadow-sm">
                      <svg width="12" height="12" viewBox="0 0 20 20" fill="none"><path d="M7.5 13.5L4 10l1.41-1.41L7.5 10.67l7.09-7.09L16 4l-8.5 9.5z" fill="currentColor" /></svg>
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <div className={`font-medium text-lg ${isSelected ? 'text-blue-700' : 'group-hover:text-blue-600'}`}>{tpl.name}</div>
                    <div className={`px-2 py-0.5 rounded-full text-xs ${
                      tpl.category === 'event'
                        ? 'bg-blue-100 text-blue-700'
                        : tpl.category === 'sport'
                          ? 'bg-green-100 text-green-700'
                          : tpl.category === 'service'
                            ? 'bg-purple-100 text-purple-700'
                            : 'bg-red-100 text-red-700'
                    }`}
                    >
                      {tpl.category}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">{tpl.description}</div>
                </div>
              </button>
            )
          })}
        </div>
      </div>
      <style jsx>
        {`
        .custom-scrollbar::-webkit-scrollbar { width: 6px; background: #f1f1f1; }
        .custom-scrollbar::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 6px; }
      `}
      </style>
    </div>
  )
})

TemplateSelector.displayName = 'TemplateSelector'

export default TemplateSelector
