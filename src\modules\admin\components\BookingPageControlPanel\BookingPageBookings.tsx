'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Download, Filter, Plus, Search } from 'lucide-react'
import React, { useState } from 'react'

export interface BookingItem {
  id: string
  customerName: string
  customerPhone: string
  customerEmail: string
  date: string
  time: string
  duration: number
  status: string
  createdAt: string
  notes: string
}

interface BookingPageBookingsProps {
  bookingPageId: string
  bookings?: BookingItem[]
}

/**
 * Bookings tab for the booking page control panel
 * Shows bookings for the page and allows filtering and management
 */
const BookingPageBookings: React.FC<BookingPageBookingsProps> = ({ bookings: externalBookings }) => {
  const [bookingTab, setBookingTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')

  // Sử dụng bookings từ props hoặc dữ liệu mẫu
  const bookings = externalBookings || [
    {
      id: 'b1',
      customerName: 'Nguyễn Văn A',
      customerPhone: '0901234567',
      customerEmail: '<EMAIL>',
      date: '2023-06-15',
      time: '18:00 - 20:00',
      duration: 120,
      status: 'confirmed',
      createdAt: '2023-06-10T10:30:00Z',
      notes: 'Khách hàng VIP',
    },
    {
      id: 'b2',
      customerName: 'Trần Thị B',
      customerPhone: '0909876543',
      customerEmail: '<EMAIL>',
      date: '2023-06-16',
      time: '08:00 - 10:00',
      duration: 120,
      status: 'pending',
      createdAt: '2023-06-11T14:20:00Z',
      notes: '',
    },
    {
      id: 'b3',
      customerName: 'Lê Văn C',
      customerPhone: '0905555555',
      customerEmail: '<EMAIL>',
      date: '2023-06-17',
      time: '14:00 - 16:00',
      duration: 120,
      status: 'confirmed',
      createdAt: '2023-06-12T09:15:00Z',
      notes: 'Đặt 2 sân liên tiếp',
    },
  ]

  // Filter bookings based on tab, search, and filters
  const filteredBookings = bookings.filter((booking: BookingItem) => {
    // Filter by tab
    if (bookingTab === 'pending' && booking.status !== 'pending') {
      return false
    }
    if (bookingTab === 'confirmed' && booking.status !== 'confirmed') {
      return false
    }
    if (bookingTab === 'cancelled' && booking.status !== 'cancelled') {
      return false
    }

    // Filter by search query
    if (searchQuery && !booking.customerName.toLowerCase().includes(searchQuery.toLowerCase())
      && !booking.customerPhone.includes(searchQuery)
      && !booking.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }

    // Filter by status
    if (statusFilter !== 'all' && booking.status !== statusFilter) {
      return false
    }

    // Filter by date (simplified for demo)
    const today = new Date().toISOString().split('T')[0]
    const tomorrow = new Date(Date.now() + 86400000).toISOString().split('T')[0]

    if (dateFilter === 'today' && booking.date !== today) {
      return false
    }
    if (dateFilter === 'tomorrow' && booking.date !== tomorrow) {
      return false
    }

    // Simplified week filter - in a real app, this would be more sophisticated
    if (dateFilter === 'thisWeek') {
      const bookingDate = new Date(booking.date)
      const now = new Date()
      const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))
      const endOfWeek = new Date(now.setDate(now.getDate() + 6))

      if (bookingDate < startOfWeek || bookingDate > endOfWeek) {
        return false
      }
    }

    return true
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Quản lý đặt lịch</h2>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            Xuất Excel
          </Button>
          <Button className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            Tạo đặt lịch
          </Button>
        </div>
      </div>

      <Tabs value={bookingTab} onValueChange={setBookingTab}>
        <TabsList className="grid grid-cols-4 w-full md:w-[400px]">
          <TabsTrigger value="all">Tất cả</TabsTrigger>
          <TabsTrigger value="pending">Chờ xác nhận</TabsTrigger>
          <TabsTrigger value="confirmed">Đã xác nhận</TabsTrigger>
          <TabsTrigger value="cancelled">Đã hủy</TabsTrigger>
        </TabsList>
      </Tabs>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="relative flex-1 w-full md:max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm theo tên, email, số điện thoại..."
                className="pl-10"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả trạng thái</SelectItem>
                  <SelectItem value="pending">Chờ xác nhận</SelectItem>
                  <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                  <SelectItem value="cancelled">Đã hủy</SelectItem>
                </SelectContent>
              </Select>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Thời gian" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả thời gian</SelectItem>
                  <SelectItem value="today">Hôm nay</SelectItem>
                  <SelectItem value="tomorrow">Ngày mai</SelectItem>
                  <SelectItem value="thisWeek">Tuần này</SelectItem>
                  <SelectItem value="thisMonth">Tháng này</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Khách hàng</TableHead>
                  <TableHead>Thời gian</TableHead>
                  <TableHead>Trạng thái</TableHead>
                  <TableHead>Ghi chú</TableHead>
                  <TableHead className="text-right">Thao tác</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBookings.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                          Không có đặt lịch nào phù hợp với bộ lọc
                        </TableCell>
                      </TableRow>
                    )
                  : (
                      filteredBookings.map((booking: BookingItem) => (
                        <TableRow key={booking.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{booking.customerName}</div>
                              <div className="text-sm text-gray-500">{booking.customerPhone}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              <div>
                                <div>{booking.date}</div>
                                <div className="text-sm text-gray-500">{booking.time}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                booking.status === 'confirmed'
                                  ? 'default'
                                  : booking.status === 'pending' ? 'outline' : 'destructive'
                              }
                            >
                              {booking.status === 'confirmed'
                                ? 'Đã xác nhận'
                                : booking.status === 'pending' ? 'Chờ xác nhận' : 'Đã hủy'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-[200px] truncate">
                              {booking.notes || <span className="text-gray-400">Không có</span>}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm">Chi tiết</Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default BookingPageBookings
