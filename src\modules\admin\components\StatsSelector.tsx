'use client'

import type { StatsTimeframe } from '@/modules/admin/utils/booking-stats'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import React from 'react'

type ChartType = 'bar' | 'line'

type StatsSelectorProps = {
  timeframe: StatsTimeframe
  chartType: ChartType
  onTimeframeChange: (timeframe: StatsTimeframe) => void
  onChartTypeChange: (chartType: ChartType) => void
}

export function StatsSelector({
  timeframe,
  chartType,
  onTimeframeChange,
  onChartTypeChange,
}: StatsSelectorProps) {
  return (
    <div className="flex space-x-4">
      <div className="inline-flex rounded-md border">
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'rounded-l-md rounded-r-none border-r',
            timeframe === 'week' && 'bg-blue-50 text-blue-600',
          )}
          onClick={() => onTimeframeChange('week')}
        >
          Theo t<PERSON>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'rounded-l-none rounded-r-md',
            timeframe === 'month' && 'bg-blue-50 text-blue-600',
          )}
          onClick={() => onTimeframeChange('month')}
        >
          Theo tháng
        </Button>
      </div>

      <div className="inline-flex rounded-md border">
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'rounded-l-md rounded-r-none border-r',
            chartType === 'bar' && 'bg-blue-50 text-blue-600',
          )}
          onClick={() => onChartTypeChange('bar')}
        >
          Biểu đồ cột
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'rounded-l-none rounded-r-md',
            chartType === 'line' && 'bg-blue-50 text-blue-600',
          )}
          onClick={() => onChartTypeChange('line')}
        >
          Biểu đồ đường
        </Button>
      </div>
    </div>
  )
}
