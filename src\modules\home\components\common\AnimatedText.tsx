'use client'

import type { ReactNode } from 'react'
import { motion } from 'framer-motion'

type AnimatedTextProps = {
  children: ReactNode
  className?: string
  delay?: number
}

export const AnimatedText = ({ children, className = '', delay = 0 }: AnimatedTextProps) => {
  return (
    <motion.p
      className={className}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      {children}
    </motion.p>
  )
}
