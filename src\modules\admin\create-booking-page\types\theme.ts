/**
 * Layout types for booking page templates
 */
export enum LayoutType {
  VERTICAL = 'vertical',
  TWO_COLUMN = '2-column',
  SPORT_BASIC = 'sport-basic',
  BANNER_SPLIT_MAP = 'banner-split-map', // <PERSON> trên cùng, 30-70 ở giữa, <PERSON> dưới cùng
}

/**
 * Theme settings interface for booking page templates
 */
export interface ThemeSettings {
  primaryColor: string
  fontFamily: string
  layout: LayoutType
  secondaryColor?: string
  textColor?: string
  backgroundColor?: string
  headerStyle?: 'default' | 'transparent' | 'sticky'
  footerStyle?: 'default' | 'minimal' | 'none'
  borderRadius?: string
  spacing?: 'compact' | 'normal' | 'spacious'
  animation?: string
  customCSS?: string
}
