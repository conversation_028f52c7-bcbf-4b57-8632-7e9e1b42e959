'use client'

import React, { memo } from 'react'
// Import will be needed when we add template-specific customizations
// import { useBookingPageConfigStore } from '../stores/booking-page-config'
import BlockBasedTemplatePreview from './BlockBasedTemplatePreview'
import MacBookMock from './MacBookMock'

const BookingPageReview = memo(() => {
  // We can access the store if needed for future template-specific customizations
  // const { templateSelected } = useBookingPageConfigStore()

  // Render template preview using block-based system
  const renderTemplatePreview = () => {
    // For now, all templates use the block-based system
    // In the future, you can add conditions based on templateSelected.templateCode
    // to render different specialized BlockBasedTemplatePreview components
    return <BlockBasedTemplatePreview />
  }

  return (
    <>
      {/* Preview */}
      <div className="mb-8">
        <MacBookMock>
          {renderTemplatePreview()}
        </MacBookMock>
      </div>
    </>
  )
})

BookingPageReview.displayName = 'BookingPageReview'

export default BookingPageReview
