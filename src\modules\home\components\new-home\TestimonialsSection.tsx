'use client'

import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import React, { useEffect, useRef } from 'react'
import { FaQuoteLeft, FaStar } from 'react-icons/fa'

// Testimonial data
const testimonials = [
  {
    id: 1,
    name: 'Nguyễn Văn <PERSON>',
    role: 'Chủ sân bóng đá',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    content:
      'PickSlot đã giúp tôi tăng lượng đặt sân lên 40% chỉ trong tháng đầu tiên. Khách hàng rất thích sự tiện lợi khi đặt sân trực tuyến.',
    rating: 5,
  },
  {
    id: 2,
    name: 'Trầ<PERSON> Thị B',
    role: 'Quản lý nhà hàng',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    content:
      'Tôi đã tiết kiệm được rất nhiều thời gian khi không phải trả lời điện thoại đặt bàn. <PERSON><PERSON> thống hoạt động mượt mà và khách hàng rất hài lòng.',
    rating: 5,
  },
  {
    id: 3,
    name: 'Lê Văn C',
    role: 'Chủ CLB cầu lông',
    avatar: 'https://randomuser.me/api/portraits/men/62.jpg',
    content:
      'Giao diện đẹp và dễ sử dụng. Tôi có thể tùy chỉnh mọi thứ theo ý muốn và theo dõi số liệu một cách dễ dàng.',
    rating: 4,
  },
  {
    id: 4,
    name: 'Phạm Thị D',
    role: 'Quản lý spa',
    avatar: 'https://randomuser.me/api/portraits/women/26.jpg',
    content:
      'PickSlot đã thay đổi hoàn toàn cách chúng tôi quản lý lịch hẹn. Khách hàng có thể đặt lịch 24/7 và chúng tôi không bao giờ bỏ lỡ một cuộc hẹn nào.',
    rating: 5,
  },
]

// Duplicate testimonials to create a continuous flow
const duplicatedTestimonials = [...testimonials, ...testimonials]

// Define a custom type for the marquee ref with animation property
interface MarqueeRef extends HTMLDivElement {
  animation?: gsap.core.Tween
}

export const TestimonialsSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const marqueeRef = useRef<MarqueeRef>(null)
  const isHoveringRef = useRef(false)

  // Handle mouse hover to pause animation
  const handleMouseEnter = () => {
    isHoveringRef.current = true
    // Pause the animation
    gsap.to(marqueeRef.current, { timeScale: 0, duration: 0.5 })
  }

  const handleMouseLeave = () => {
    isHoveringRef.current = false
    // Resume the animation
    gsap.to(marqueeRef.current, { timeScale: 1, duration: 0.5 })
  }

  // Initialize animations
  useEffect(() => {
    // Register ScrollTrigger
    gsap.registerPlugin(ScrollTrigger)

    const ctx = gsap.context(() => {
      // Animate title
      gsap.from(titleRef.current, {
        y: 30,
        opacity: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
        },
      })

      // Calculate the total width of all testimonials
      const testimonialCards = document.querySelectorAll('.testimonial-card')
      let totalWidth = 0

      testimonialCards.forEach((card) => {
        totalWidth += (card as HTMLElement).offsetWidth + 16 // 16px for gap
      })

      // Create continuous horizontal scrolling animation
      const marqueeAnimation = gsap.to(marqueeRef.current, {
        x: `-${totalWidth / 2}px`, // Move by half the width (since we duplicated the items)
        duration: 30, // Adjust speed here - higher number = slower
        ease: 'none',
        repeat: -1, // Infinite repeat
        onRepeat: () => {
          // Reset position to start for seamless loop
          gsap.set(marqueeRef.current, { x: 0 })
        },
      })

      // Store the animation in the ref for pause/resume
      marqueeRef.current!.animation = marqueeAnimation
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section
      ref={sectionRef}
      className="py-16 bg-gradient-to-b from-white to-orange-50"
    >
      <div className="container mx-auto px-4">
        <h2
          ref={titleRef}
          className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-800"
        >
          Khách hàng nói gì về chúng tôi
        </h2>

        {/* Testimonial marquee */}
        <div
          className="overflow-hidden"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div
            ref={marqueeRef}
            className="flex gap-4"
          >
            {duplicatedTestimonials.map((testimonial, index) => (
              <div
                key={`${testimonial.id}-${index}`}
                className="testimonial-card flex flex-col flex-shrink-0 w-80 bg-white rounded-lg p-6 shadow-md"
              >
                <div className="mb-4 relative flex-1">
                  <FaQuoteLeft className="text-orange-200 text-3xl absolute -top-2 -left-2" />
                  <p className="text-gray-600 relative z-10 pl-6">{testimonial.content}</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-orange-200"
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                        e.currentTarget.src = `https://ui-avatars.com/api/?name=${testimonial.name}&background=random`
                      }}
                    />
                    <div>
                      <h4 className="font-bold">{testimonial.name}</h4>
                      <p className="text-sm text-gray-500">{testimonial.role}</p>
                    </div>
                  </div>

                  <div className="flex">
                    {[...Array.from({ length: 5 })].map((_, i) => (
                      <FaStar
                        key={`star-${testimonial.id}-${index}-${i}`}
                        className={i < testimonial.rating ? 'text-yellow-400' : 'text-gray-200'}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
