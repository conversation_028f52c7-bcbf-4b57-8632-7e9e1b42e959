'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useRouter } from '@/libs/i18nNavigation'
import gsap from 'gsap'
import { CalendarX } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useEffect, useRef } from 'react'

type BookingPageNotAvailableProps = {
  /**
   * Slug của booking page
   */
  slug: string
}

export default function BookingPageNotAvailable({ slug }: BookingPageNotAvailableProps) {
  const router = useRouter()
  const t = useTranslations('BookingPageNotAvailable')
  const containerRef = useRef<HTMLDivElement>(null)
  const iconRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  // Simple entrance animations
  useEffect(() => {
    if (containerRef.current && iconRef.current && contentRef.current) {
      // Create animation timeline with subtle easing
      const tl = gsap.timeline({ defaults: { ease: 'power2.out' } })

      // Animate the container with a simple fade in
      tl.from(containerRef.current, {
        opacity: 0,
        y: 20,
        duration: 0.6,
      })

      // Animate the icon with a subtle entrance
      tl.from(iconRef.current, {
        scale: 0.9,
        opacity: 0,
        duration: 0.5,
      }, '-=0.3')

      // Simple fade for content elements
      tl.from(contentRef.current.children, {
        y: 10,
        opacity: 0,
        stagger: 0.1,
        duration: 0.5,
      }, '-=0.3')
    }
  }, [])

  // Home icon
  const homeIcon = (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  )

  // Back button icon
  const backIcon = (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="m12 19-7-7 7-7" />
      <path d="M19 12H5" />
    </svg>
  )

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background via-background to-primary/5 p-4 sm:p-8">
      {/* Single subtle decorative element */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 inset-x-0 h-1/3 bg-gradient-to-b from-primary/5 to-transparent" />
      </div>

      {/* Main content card */}
      <Card
        ref={containerRef}
        className="backdrop-blur-sm bg-background/95 shadow-md border-primary/10 p-6 sm:p-8 max-w-md w-full"
      >
        <div className="flex flex-col items-center text-center space-y-6">
          {/* Icon container */}
          <div
            ref={iconRef}
            className="bg-primary/10 p-5 rounded-full"
          >
            <CalendarX className="h-10 w-10 text-primary-foreground" />
          </div>

          {/* Content section with clean typography */}
          <div ref={contentRef} className="space-y-3">
            <h1 className="text-3xl font-bold tracking-tight text-foreground">
              {t('title', { fallback: 'Booking Page Not Available' })}
            </h1>
            <h2 className="text-xl font-medium text-foreground text-primary">
              {slug}
            </h2>
            <p className="text-muted-foreground text-sm max-w-md mx-auto">
              {t('description', { fallback: 'This booking page has not been configured yet or is temporarily unavailable. Please check back later or contact the page owner.' })}
            </p>
          </div>

          {/* Buttons with minimal animations */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 w-full">
            <Button
              variant="outline"
              className="flex-1 gap-2"
              onClick={() => router.back()}
            >
              {backIcon}
              {t('back_button', { fallback: 'Go Back' })}
            </Button>
            <Button
              className="flex-1 gap-2 bg-primary hover:bg-primary/90 text-primary-foreground"
              onClick={() => router.push('/')}
            >
              {homeIcon}
              {t('home_button', { fallback: 'Go Home' })}
            </Button>
          </div>

          {/* Help text */}
          <div className="text-xs text-muted-foreground pt-2">
            <p>
              {t('help_text', { fallback: 'If you believe this is an error, please contact the booking page owner.' })}
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}
