import RegisterScreen from '@/modules/auth/screens/RegisterScreen'
import { getTranslations, setRequestLocale } from 'next-intl/server'

type IRegisterPageProps = {
  params: Promise<{ locale: string }>
}

export async function generateMetadata(props: IRegisterPageProps) {
  const { locale } = await props.params
  const t = await getTranslations({
    locale,
    namespace: 'SignUp',
  })

  return {
    title: t('meta_title', { fallback: 'Create Account - Booking Easy' }),
    description: t('meta_description', { fallback: 'Create a new account to start using our booking platform' }),
  }
}

export default async function RegisterPage(props: IRegisterPageProps) {
  const { locale } = await props.params
  setRequestLocale(locale)

  return (
    <RegisterScreen />
  )
}
