import type { Block } from '../types/blocks'
import type { LayoutType } from '../types/theme'
import debounce from 'lodash/debounce'
import { useEffect, useRef } from 'react'
import { toast } from 'sonner'
import { useBookingPageConfigStore } from '../stores/booking-page-config'

/**
 * Hook for managing template configuration
 * Works with any template type
 */
export const useTemplateConfig = () => {
  const {
    templateSelected,
    blocks,
    updateBlock,
    addBlock,
    removeBlock,
    reorderBlocks,
    themeSettings,
    setThemeSettings,
  } = useBookingPageConfigStore()

  // Create a ref for the debounced function to persist between renders
  const debouncedUpdateRef = useRef(
    debounce(() => {
      // This could be used to auto-save changes or sync with backend
      // For now, we'll just log
      // eslint-disable-next-line no-console
      console.log('Template config updated')
    }, 500),
  )

  // Update when blocks or theme settings change
  useEffect(() => {
    // Store a reference to the current debounced function
    const debouncedFn = debouncedUpdateRef.current
    debouncedFn()

    return () => {
      debouncedFn.cancel()
    }
  }, [blocks, themeSettings])

  // Handle block change
  const handleBlockChange = (index: number, updatedBlock: Block) => {
    updateBlock(index, updatedBlock)
  }

  // Handle layout change
  const handleLayoutChange = (layout: LayoutType) => {
    setThemeSettings({ layout })
  }

  // Handle color change
  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setThemeSettings({ primaryColor: e.target.value })
  }

  // Handle font change
  const handleFontChange = (fontFamily: string) => {
    setThemeSettings({ fontFamily })
  }

  // Handle save
  const handleSave = () => {
    // Here you could implement saving to backend
    toast.success(`Cấu hình template ${templateSelected.name} đã được lưu`, {
      description: 'Các thay đổi đã được áp dụng cho trang đặt vé.',
    })
  }

  return {
    templateSelected,
    blocks,
    themeSettings,
    handleBlockChange,
    handleLayoutChange,
    handleColorChange,
    handleFontChange,
    handleSave,
    addBlock,
    removeBlock,
    reorderBlocks,
  }
}
