import mainApi from '@/apis/mainApi'

export interface RegisterPayload {
  name: string
  email: string
  password: string
}

export interface LoginPayload {
  email: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  expiresIn: string
}

export const authAPIs = {
  register: (payload: RegisterPayload) => mainApi.post('/auth/signup', payload),
  login: (payload: LoginPayload) => mainApi.post<LoginResponse>('/auth/signin', payload),
  googleLogin: (payload: { idToken: string }) => mainApi.post<LoginResponse>('/auth/google-signin', payload),
  resendVerification: (payload: { email: string }) => mainApi.post('/auth/resend-verification', payload),
  verifyEmail: (token: string) => mainApi.post('/auth/verify-email', { token }),
}
