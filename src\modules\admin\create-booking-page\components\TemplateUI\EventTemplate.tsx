/* eslint-disable react-dom/no-missing-iframe-sandbox */
import React from 'react'

// Helper function to adjust color brightness
const adjustColor = (color: string, amount: number): string => {
  // Remove # if present
  color = color.replace('#', '')

  // Parse the color
  const r = Number.parseInt(color.substring(0, 2), 16)
  const g = Number.parseInt(color.substring(2, 4), 16)
  const b = Number.parseInt(color.substring(4, 6), 16)

  // Adjust each component
  const adjustR = Math.max(0, Math.min(255, r + amount))
  const adjustG = Math.max(0, Math.min(255, g + amount))
  const adjustB = Math.max(0, Math.min(255, b + amount))

  // Convert back to hex
  return `#${adjustR.toString(16).padStart(2, '0')}${adjustG.toString(16).padStart(2, '0')}${adjustB.toString(16).padStart(2, '0')}`
}

export type EventTemplateConfig = {
  themeColor: string
  banner: string
  title: string
  venue: string
  time: string
  price: number
  slots: number
  mapURL: string
  paymentOptions: string[]
}

type Props = {
  config?: EventTemplateConfig
}

const EventTemplate = ({ config }: Props) => {
  // Default values if no config is provided
  const {
    themeColor = '#2563eb',
    banner = '/event-banners/chatgpt.jpg',
    title = 'Làm Chủ ChatGPT Trong Công Việc',
    venue = 'Toong Vista Verde, Q2',
    time = '12/05/2025 15:00',
    price = 199000,
    slots = 12,
    mapURL = 'https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d2771.081803717511!2d106.76085015824412!3d10.818851941588354!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x317527ffac7da4ab%3A0xff3d9144caa65d37!2sHim%20Lam%20Ph%C3%BA%20An!5e0!3m2!1svi!2s!4v1745510383953!5m2!1svi!2s',
    paymentOptions,
  } = config || {}

  // Format price to VND
  const formattedPrice = new Intl.NumberFormat('vi-VN').format(price)

  const renderPayment = () => {
    return (
      <>
        {paymentOptions && paymentOptions.length > 0 && (
          <div className="block">
            <span className="text-sm block mb-2">Phương thức thanh toán:</span>
            <div className="flex flex-wrap gap-2">
              {paymentOptions.map(option => (
                <label key={option} className="flex items-center gap-2">
                  <input type="radio" name="payment" value={option} className="accent-green-600" />
                  <span>{option}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </>
    )
  }

  const renderMap = () => {
    return (
      <>
        {mapURL && (
          <>
            <h3 className="text-lg font-medium mt-6">📍 Hướng dẫn di chuyển</h3>
            <div className="w-full rounded-lg border overflow-hidden">
              <iframe title="x" src={mapURL} width="100%" height="450" allowFullScreen loading="lazy" referrerPolicy="no-referrer-when-downgrade"></iframe>
            </div>
          </>
        ) }

      </>
    )
  }

  return (
    <main className="max-w-3xl mx-auto p-4 space-y-10">
      <section className="space-y-4 text-center">
        <div className="relative w-full h-64 rounded-xl shadow overflow-hidden">
          {/* Using a div with background image instead of Image component for simplicity */}
          <div
            className="w-full h-full bg-cover bg-center rounded-xl"
            style={{ backgroundImage: `url(${banner})` }}
            role="img"
            aria-label="Event banner"
          />
        </div>
        <h1 className="text-2xl font-bold">{title}</h1>
        <p className="text-gray-600">
          📍
          {' '}
          {venue}
          {' '}
          | 🕒
          {' '}
          {time}
        </p>
        <p className="text-green-600 font-medium">
          🎫 Số lượng:
          {' '}
          {slots}
          {' '}
          chỗ | 💵
          {' '}
          {formattedPrice}
          đ
        </p>
      </section>

      <section className="space-y-4">
        <h2 className="text-xl font-semibold">📝 Giới thiệu sự kiện</h2>
        <p>Buổi workshop hướng dẫn cách ứng dụng ChatGPT vào công việc văn phòng, thực hành prompt và networking.</p>

        {renderMap()}
      </section>

      <section className="space-y-4">
        <h2 className="text-xl font-semibold">🎫 Chọn vé & Thanh toán</h2>
        <form className="grid gap-4">
          <label className="block">
            <span className="text-sm">Số lượng vé:</span>
            <input type="number" min="1" max="10" defaultValue="1" className="w-20 p-2 border rounded" />
          </label>

          <label className="block">
            <span className="text-sm">Email nhận vé:</span>
            <input type="email" className="w-full p-2 border rounded" placeholder="<EMAIL>" />
          </label>

          {renderPayment()}

          <button
            type="button"
            className="px-6 py-2 rounded-lg text-white transition"
            style={{ backgroundColor: themeColor, borderColor: themeColor }}
            onMouseOver={e => e.currentTarget.style.backgroundColor = adjustColor(themeColor, -20)}
            onMouseOut={e => e.currentTarget.style.backgroundColor = themeColor}
            onFocus={e => e.currentTarget.style.backgroundColor = adjustColor(themeColor, -20)}
            onBlur={e => e.currentTarget.style.backgroundColor = themeColor}
          >
            Đặt vé ngay
          </button>
        </form>
      </section>

      <section className="bg-green-100 p-4 rounded-lg hidden" id="success">
        🎉 Đặt vé thành công! Mã vé đã được gửi qua email. Gặp bạn tại sự kiện nhé!
      </section>
    </main>
  )
}

export default EventTemplate
