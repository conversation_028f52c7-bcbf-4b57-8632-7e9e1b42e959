'use client'

import { gsap } from 'gsap'
import { ScrollToPlugin } from 'gsap/ScrollToPlugin'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { TextPlugin } from 'gsap/TextPlugin'
import React, { useEffect, useRef } from 'react'
import { BenefitsSection } from '../components/new-home/BenefitsSection'
import { CtaSection } from '../components/new-home/CtaSection'
import { FeatureSection } from '../components/new-home/FeatureSection'
import { FooterSection } from '../components/new-home/FooterSection'
import { HeaderNavigation } from '../components/new-home/HeaderNavigation'
import { HeroSection } from '../components/new-home/HeroSection'
import { TestimonialsSection } from '../components/new-home/TestimonialsSection'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin, TextPlugin)

const NewHomeScreen = () => {
  const mainRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Initialize smooth scrolling
    const ctx = gsap.context(() => {
      // Set up smooth scrolling
      gsap.to('html, body', {
        scrollBehavior: 'smooth',
        duration: 0.5,
      })

      // Initialize ScrollTrigger
      ScrollTrigger.defaults({
        markers: false, // Set to true for debugging
        toggleActions: 'play none none reverse',
      })

      // Clean up ScrollTrigger on component unmount
      return () => {
        ScrollTrigger.getAll().forEach(trigger => trigger.kill())
      }
    }, mainRef)

    return () => ctx.revert()
  }, [])

  return (
    <div ref={mainRef} className="overflow-hidden">
      <HeaderNavigation />
      <HeroSection />
      <FeatureSection />
      <BenefitsSection />
      <TestimonialsSection />
      <CtaSection />
      <FooterSection />
    </div>
  )
}

export default NewHomeScreen
