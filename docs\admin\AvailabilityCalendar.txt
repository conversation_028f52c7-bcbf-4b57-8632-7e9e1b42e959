<PERSON><PERSON><PERSON> bước triển khai AvailabilityCalendar Block
AvailabilityCalendar là một block quan trọng cho phép hiển thị lịch trình và tình trạng sẵn có của sân thể thao. Dưới đây là các bước triển khai chi tiết:

1. Thi<PERSON><PERSON> kế cấu trúc dữ liệu
Đ<PERSON> tiên, chúng ta đã định nghĩa interface cho AvailabilityCalendarBlock trong file blocks.ts:

```
export interface AvailabilityCalendarBlock extends Block {
  type: 'availability_calendar'
  data: {
    title?: string
    subtitle?: string
    view?: 'month' | 'week' | 'day'
    defaultView?: 'month' | 'week' | 'day'
    minDate?: string // YYYY-MM-DD format
    maxDate?: string // YYYY-MM-DD format
    disabledDates?: string[] // Array of YYYY-MM-DD strings
    highlightedDates?: Array<{
      date: string
      color?: string
      tooltip?: string
    }>
    businessHours?: {
      start: string // HH:MM format
      end: string // HH:MM format
      daysOfWeek: number[] // 0 = Sunday, 1 = Monday, etc.
    }
    showWeekends?: boolean
    firstDayOfWeek?: 0 | 1 // 0 = Sunday, 1 = Monday
    timeSlotInterval?: number // in minutes
    showAvailabilityLegend?: boolean
  }
}
```

2. Tạo component hiển thị (AvailabilityCalendarBlockComponent)
Khởi tạo state và props:
Quản lý ngày được chọn, chế độ xem (tháng/tuần/ngày)
Xử lý các props như giờ làm việc, ngày bị vô hiệu hóa, v.v.
Tạo các hàm tiện ích:
Hàm tạo lịch tháng/tuần/ngày
Hàm kiểm tra tình trạng sẵn có của từng ngày/giờ
Hàm định dạng ngày giờ
Xây dựng giao diện chế độ xem tháng:
Hiển thị lịch dạng lưới với các ngày trong tháng
Đánh dấu ngày hiện tại, ngày được chọn
Hiển thị trạng thái sẵn có bằng màu sắc hoặc biểu tượng
Xây dựng giao diện chế độ xem tuần:
Hiển thị lịch dạng lưới với các ngày trong tuần và các khung giờ
Đánh dấu thời gian hiện tại
Hiển thị trạng thái sẵn có cho từng khung giờ
Xây dựng giao diện chế độ xem ngày:
Hiển thị lịch chi tiết cho một ngày với các khung giờ
Đánh dấu thời gian hiện tại
Hiển thị trạng thái sẵn có cho từng khung giờ
Thêm điều khiển điều hướng:
Nút chuyển đổi giữa các chế độ xem
Nút chuyển đổi giữa các tháng/tuần/ngày
Nút quay lại ngày hiện tại
Thêm chú thích trạng thái:
Hiển thị chú thích màu sắc cho các trạng thái khác nhau (trống, đã đặt, không khả dụng)
3. Tạo component cấu hình (AvailabilityCalendarBlockConfig)
Cấu hình tiêu đề và mô tả:
Input cho tiêu đề và mô tả của block
Cấu hình chế độ xem:
Chọn chế độ xem mặc định (tháng/tuần/ngày)
Cấu hình ngày bắt đầu tuần (Chủ nhật/Thứ 2)
Cấu hình giờ làm việc:
Thiết lập giờ mở cửa và đóng cửa
Chọn các ngày trong tuần hoạt động
Cấu hình ngày đặc biệt:
Thêm/xóa/sửa các ngày đặc biệt (nghỉ lễ, sự kiện)
Thiết lập màu sắc và ghi chú cho các ngày đặc biệt
Cấu hình khung giờ:
Thiết lập thời lượng của mỗi khung giờ (15, 30, 60 phút)
Cấu hình hiển thị cuối tuần
Cấu hình hiển thị:
Bật/tắt hiển thị chú thích trạng thái
Tùy chỉnh màu sắc cho các trạng thái khác nhau
4. Tích hợp với hệ thống block
Đăng ký block mới:
Thêm block vào danh sách các block có sẵn
Cập nhật các template sử dụng block này
Tích hợp với BlockConfigRenderer:
Đảm bảo block mới có thể được thêm/xóa/sửa trong giao diện cấu hình
Tích hợp với BookingPageReview:
Đảm bảo block mới hiển thị chính xác trong chế độ xem trước
5. Tối ưu hóa hiệu suất
Sử dụng useMemo và useCallback:
Tối ưu hóa việc tính toán lịch và trạng thái
Tránh tính toán lại không cần thiết
Lazy loading:
Chỉ tải dữ liệu cho chế độ xem hiện tại
Tải dữ liệu theo nhu cầu khi chuyển đổi chế độ xem
Phân trang:
Trong chế độ xem tháng, có thể phân trang nếu có quá nhiều dữ liệu
6. Tương thích với các thiết bị
Responsive design:
Đảm bảo hiển thị tốt trên desktop, tablet và mobile
Điều chỉnh layout dựa trên kích thước màn hình
Touch-friendly:
Hỗ trợ các thao tác vuốt để chuyển đổi giữa các tháng/tuần/ngày
Các nút đủ lớn để dễ dàng tương tác trên thiết bị cảm ứng
Việc triển khai AvailabilityCalendar là khá phức tạp, nhưng khi hoàn thành, nó sẽ cung cấp một công cụ mạnh mẽ cho người dùng để hiển thị và quản lý lịch trình sân thể thao. Block này có thể dễ dàng tái sử dụng cho các loại template khác như đặt dịch vụ hoặc đặt phòng.