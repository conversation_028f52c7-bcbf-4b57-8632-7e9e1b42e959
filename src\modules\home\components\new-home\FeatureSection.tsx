'use client'

import { gsap } from 'gsap'
import React, { useEffect, useRef } from 'react'
import { FaCalendarCheck, FaChartLine, FaMobileAlt, FaUsers } from 'react-icons/fa'

// Feature data
const features = [
  {
    id: 'feature-booking',
    icon: <FaCalendarCheck className="text-4xl text-orange-500" />,
    title: 'Đặt chỗ dễ dàng',
    description: '<PERSON>h<PERSON>ch hàng có thể đặt chỗ trực tuyến 24/7 mà không cần gọi điện hay nhắn tin.',
  },
  {
    id: 'feature-stats',
    icon: <FaChartLine className="text-4xl text-blue-500" />,
    title: 'Thống kê chi tiết',
    description: '<PERSON> đặt chỗ, doanh thu và các số liệu quan trọng khác trong thời gian thực.',
  },
  {
    id: 'feature-customers',
    icon: <FaUsers className="text-4xl text-green-500" />,
    title: '<PERSON>u<PERSON>n lý khách hàng',
    description: '<PERSON><PERSON><PERSON> dựng cơ sở dữ liệu khách hàng và tăng cường mối quan hệ với họ.',
  },
  {
    id: 'feature-mobile',
    icon: <FaMobileAlt className="text-4xl text-purple-500" />,
    title: 'Tương thích di động',
    description: 'Trang đặt chỗ hoạt động mượt mà trên mọi thiết bị từ máy tính đến điện thoại.',
  },
]

export const FeatureSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const featureRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate section title
      gsap.from('.section-title', {
        y: 50,
        opacity: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        },
      })

      // Animate features
      featureRefs.current.forEach((feature, index) => {
        gsap.from(feature, {
          y: 100,
          opacity: 0,
          duration: 0.8,
          delay: index * 0.2,
          scrollTrigger: {
            trigger: feature,
            start: 'top 85%',
          },
        })
      })

      // Animate feature icons with continuous rotation
      gsap.to('.feature-icon', {
        duration: 5,
        repeat: -1,
        ease: 'bounce.in',
      })
    }, sectionRef)

    return () => {
      ctx.revert()
    }
  }, [])

  return (
    <section
      ref={sectionRef}
      className="py-24 bg-white relative overflow-hidden"
    >
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 -right-20 w-64 h-64 bg-orange-100 rounded-full opacity-50" />
        <div className="absolute -bottom-32 -left-32 w-96 h-96 bg-blue-50 rounded-full opacity-50" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="section-title text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            Tính năng nổi bật
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            PickSlot cung cấp mọi công cụ bạn cần để tạo và quản lý trang đặt chỗ chuyên nghiệp
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={feature.id}
              ref={(el: HTMLDivElement | null) => {
                featureRefs.current[index] = el
              }}
              className="feature-card bg-white rounded-xl p-8 shadow-lg border border-gray-100
                transition-all duration-300 hover:translate-y-[-15px] hover:scale-105
                hover:shadow-xl group"
            >
              <div className="feature-icon mb-6 transform perspective-500 transition-transform duration-300
                group-hover:scale-110 group-hover:rotate-y-10 group-hover:rotate-x-10"
              >
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold mb-3 transition-colors duration-300 group-hover:text-orange-500">
                {feature.title}
              </h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
