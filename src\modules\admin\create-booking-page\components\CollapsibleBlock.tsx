'use client'

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { cn } from '@/libs/utils'
import { ChevronDown, ChevronUp } from 'lucide-react'
import React, { useState } from 'react'

interface CollapsibleBlockProps {
  title: string
  defaultOpen?: boolean
  className?: string
  children: React.ReactNode
  actions?: React.ReactNode
}

/**
 * CollapsibleBlock Component
 *
 * A reusable component for creating collapsible sections in the admin UI
 */
const CollapsibleBlock: React.FC<CollapsibleBlockProps> = ({
  title,
  defaultOpen = true,
  className,
  children,
  actions,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={cn('border rounded-lg p-4 bg-white', className)}
    >
      <div className="flex justify-between items-center w-full">
        <CollapsibleTrigger className="flex items-center gap-2 flex-1">
          <h3 className="font-semibold">{title}</h3>
          {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </CollapsibleTrigger>
        {actions && <div className="flex items-center">{actions}</div>}
      </div>
      <CollapsibleContent className="pt-4">
        {children}
      </CollapsibleContent>
    </Collapsible>
  )
}

export default CollapsibleBlock
