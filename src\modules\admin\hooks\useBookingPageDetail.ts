import type { BookingPageItem } from '../apis/booking-page.api'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../apis/booking-page.api'
import { useBookingPageDetailStore } from '../stores/booking-page-detail.store'

// Stats interface
export interface BookingPageStats {
  totalViews: number
  totalBookings: number
  conversionRate: number
  averageBookingDuration: number
  popularTimes: string[]
  recentBookings: {
    id: string
    customerName: string
    time: string
    status: string
  }[]
}

// Booking item interface
export interface BookingItem {
  id: string
  customerName: string
  customerPhone: string
  customerEmail: string
  date: string
  time: string
  duration: number
  status: string
  createdAt: string
  notes: string
}

/**
 * Hook to fetch and manage booking page details
 * Uses Zustand store for bookingPage data and local state for stats and bookings
 */
export const useBookingPageDetail = (bookingPageId: string) => {
  // Use the store for bookingPage data
  const {
    bookingPage,
    error: storeError,
    setBookingPage,
    updateBookingPage: updateStoreBookingPage,
    setError,
  } = useBookingPageDetailStore()

  // Use local state for stats and bookings
  const [stats, setStats] = useState<BookingPageStats | null>(null)
  const [bookings, setBookings] = useState<BookingItem[]>([])

  const [isLoading, setIsLoading] = useState(true)

  // Fetch booking page details
  const fetchBookingPageDetails = useCallback(async () => {
    if (!bookingPageId) {
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Trong môi trường thực tế, gọi API để lấy dữ liệu
      const response = await bookingPageAPIs.getBookingPageById(bookingPageId)
      const mockBookingPage = response?.data

      // Mock stats data for now (in a real app, this would come from the API)
      const mockStats: BookingPageStats = {
        totalViews: 245,
        totalBookings: 32,
        conversionRate: 13,
        averageBookingDuration: 60,
        popularTimes: ['18:00 - 20:00', '08:00 - 10:00'],
        recentBookings: [
          { id: 'b1', customerName: 'Nguyễn Văn A', time: '2 giờ trước', status: 'confirmed' },
          { id: 'b2', customerName: 'Trần Thị B', time: '5 giờ trước', status: 'pending' },
          { id: 'b3', customerName: 'Lê Văn C', time: '1 ngày trước', status: 'confirmed' },
        ],
      }

      // Mock bookings data for now
      const mockBookings: BookingItem[] = [
        {
          id: 'b1',
          customerName: 'Nguyễn Văn A',
          customerPhone: '0901234567',
          customerEmail: '<EMAIL>',
          date: '2023-06-15',
          time: '18:00 - 20:00',
          duration: 120,
          status: 'confirmed',
          createdAt: '2023-06-10T10:30:00Z',
          notes: 'Khách hàng VIP',
        },
        {
          id: 'b2',
          customerName: 'Trần Thị B',
          customerPhone: '0909876543',
          customerEmail: '<EMAIL>',
          date: '2023-06-16',
          time: '08:00 - 10:00',
          duration: 120,
          status: 'pending',
          createdAt: '2023-06-11T14:20:00Z',
          notes: '',
        },
        {
          id: 'b3',
          customerName: 'Lê Văn C',
          customerPhone: '0905555555',
          customerEmail: '<EMAIL>',
          date: '2023-06-17',
          time: '14:00 - 16:00',
          duration: 120,
          status: 'confirmed',
          createdAt: '2023-06-12T09:15:00Z',
          notes: 'Đặt 2 sân liên tiếp',
        },
      ]

      // Giả lập API thành công
      const mockResponse = { data: mockBookingPage, status: { success: true } }

      if (mockResponse && mockResponse.data) {
        // Update store with bookingPage data
        setBookingPage(mockResponse.data)

        // Update local state for stats and bookings
        setStats(mockStats)
        setBookings(mockBookings)
      } else {
        setError('Không thể tải thông tin booking page')
        toast.error('Không thể tải thông tin booking page')
      }
    } catch (err) {
      console.error('Failed to fetch booking page details:', err)
      setError('Không thể tải thông tin booking page. Vui lòng thử lại sau.')
      toast.error('Không thể tải thông tin booking page')
    } finally {
      setIsLoading(false)
    }
  }, [bookingPageId, setBookingPage, setError])

  // Update booking page
  const updateBookingPage = async (updatedData: Partial<BookingPageItem>) => {
    if (!bookingPageId || !bookingPage) {
      return false
    }

    try {
      // Trong môi trường thực tế, gọi API để cập nhật
      // const response = await bookingPageAPIs.updateBookingPage(bookingPageId, updatedData)

      // Giả lập API thành công
      const mockResponse = { status: { success: true } }

      if (mockResponse && mockResponse.status?.success) {
        // Update store state
        updateStoreBookingPage(updatedData)

        toast.success('Cập nhật thành công')
        return true
      } else {
        toast.error('Không thể cập nhật booking page')
        return false
      }
    } catch (err) {
      console.error('Failed to update booking page:', err)
      toast.error('Không thể cập nhật booking page')
      return false
    }
  }

  // Load booking page details on mount and when ID changes
  useEffect(() => {
    if (bookingPageId) {
      fetchBookingPageDetails()
    }
  }, [bookingPageId, fetchBookingPageDetails])

  // Combine data for backward compatibility
  const data = {
    bookingPage,
    stats,
    bookings,
  }

  return {
    data,
    bookingPage,
    stats,
    bookings,
    isLoading,
    error: storeError,
    fetchBookingPageDetails,
    updateBookingPage,
  }
}

export default useBookingPageDetail
