import mainApi from '@/apis/mainApi'

export interface UserProfile {
  id: string
  name: string
  email: string
  avatar?: string
  createdAt?: string
  updatedAt?: string
}

export interface UserProfileResponse {
  status: {
    success: boolean
    message?: string
    code?: string
  }
  data?: UserProfile
}

export const userAPIs = {
  // Get user profile - used to validate token
  getProfile: () => mainApi.get<UserProfileResponse>('/user/profile'),
}
