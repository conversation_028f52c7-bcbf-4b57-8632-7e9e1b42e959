import { useCallback } from 'react'
import { BOOKING_PAGE_TEMPLATES } from '../constants/templates'
import { useBookingPageConfigStore } from '../stores/booking-page-config'

export const useTemplateSelection = () => {
  const { templateSelected, setTemplateSelected } = useBookingPageConfigStore()

  // Get the selected template
  const selectedTemplate = BOOKING_PAGE_TEMPLATES.find(t => t.id === templateSelected.id)

  // Handle template selection
  const handleTemplateSelect = useCallback((templateId: string) => {
    // Find the template by ID
    const template = BOOKING_PAGE_TEMPLATES.find(t => t.id === templateId)

    if (template) {
      // Set the selected template in the store
      setTemplateSelected(template)
    }
  }, [setTemplateSelected])

  return {
    selectedTemplate,
    handleTemplateSelect,
  }
}
