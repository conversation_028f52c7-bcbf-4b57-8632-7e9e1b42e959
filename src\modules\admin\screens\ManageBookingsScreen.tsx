'use client'

import type { ManualBookingData } from '@/modules/admin/components/ManualBookingForm'
import type { ViewMode } from '@/modules/admin/components/ViewSelector'
import type { Booking } from '@/modules/admin/constants/booking-mock-data'
import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { BookingTable } from '@/modules/admin/components/BookingTable'
import { DateRangePicker } from '@/modules/admin/components/DateRangePicker'
import { ManualBookingForm } from '@/modules/admin/components/ManualBookingForm'
import { ViewSelector } from '@/modules/admin/components/ViewSelector'
import { BOOKING_STATUSES, MOCK_BOOKINGS } from '@/modules/admin/constants/booking-mock-data'
import { addDays, addMonths, isAfter, isBefore, startOfDay } from 'date-fns'
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner' // Nếu chưa có, có thể thay bằng alert

const ManageBookingsScreen = () => {
  const [openManualDialog, setOpenManualDialog] = useState(false)
  const [manualBookings, setManualBookings] = useState<Booking[]>([])

  // Mock socket.io: mỗi 15s thêm 1 booking mới
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date()
      const newBooking: Booking = {
        id: `socket-${now.getTime()}`,
        customerName: 'Khách mới (socket)',
        customerEmail: '',
        customerPhone: `09${Math.floor(10000000 + Math.random() * 90000000)}`,
        bookingTime: now.toISOString(),
        duration: 60,
        status: 'pending',
        pageId: '',
        pageName: 'Dịch vụ mới',
        notes: 'Booking mới từ socket.io (mock)',
        createdAt: now.toISOString(),
      }
      setManualBookings(prev => [newBooking, ...prev])
      toast('Có booking mới!')
    }, 15000)
    return () => clearInterval(interval)
  }, [])

  const [viewMode, setViewMode] = useState<ViewMode>('day')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateRange, setDateRange] = useState<{ from: Date, to: Date }>({
    from: addDays(new Date(), -7),
    to: new Date(),
  })
  const [filteredBookings, setFilteredBookings] = useState<Booking[]>([])

  // Xử lý thay đổi chế độ xem (ngày/tuần/tháng)
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode)
    const today = new Date()

    let newDateRange = { from: today, to: today }

    switch (mode) {
      case 'day':
        newDateRange = { from: today, to: today }
        break
      case 'week':
        newDateRange = { from: addDays(today, -7), to: today }
        break
      case 'month':
        newDateRange = { from: addMonths(today, -1), to: today }
        break
    }

    setDateRange(newDateRange)
  }

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: { from: Date, to: Date } | undefined) => {
    if (range) {
      setDateRange(range)
    }
  }

  // Lọc danh sách booking
  useEffect(() => {
    const allBookings = [...manualBookings, ...MOCK_BOOKINGS]
    const filtered = allBookings.filter((booking) => {
      const bookingDate = new Date(booking.bookingTime)
      const fromDate = startOfDay(dateRange.from)
      const toDate = new Date(dateRange.to)
      toDate.setHours(23, 59, 59, 999)

      // Lọc theo thời gian
      const isInDateRange = isAfter(bookingDate, fromDate) && isBefore(bookingDate, toDate)

      // Lọc theo trạng thái
      const matchesStatus = statusFilter === 'all' || booking.status === statusFilter

      // Lọc theo tìm kiếm
      const matchesSearch
        = booking.customerName.toLowerCase().includes(searchQuery.toLowerCase())
          || booking.customerPhone.includes(searchQuery)
          || booking.customerEmail.toLowerCase().includes(searchQuery.toLowerCase())
          || booking.pageName.toLowerCase().includes(searchQuery.toLowerCase())

      return isInDateRange && matchesStatus && matchesSearch
    })

    setFilteredBookings(filtered)
  }, [dateRange, statusFilter, searchQuery])

  // Handler tạo booking thủ công
  const handleManualBooking = (data: ManualBookingData) => {
    const newBooking: Booking = {
      id: `manual-${Date.now()}`,
      customerName: data.customerName,
      customerEmail: '',
      customerPhone: data.customerPhone,
      bookingTime: data.bookingTime,
      duration: 60, // mặc định 1h
      status: 'confirmed',
      pageId: '',
      pageName: data.pageName,
      notes: data.notes,
      createdAt: new Date().toISOString(),
    }
    setManualBookings(prev => [newBooking, ...prev])
    setOpenManualDialog(false)
    toast('Tạo booking thành công!')
  }

  // Handlers cho các hành động
  const handleAcceptAction = (id: string) => {
    // TODO: Thực hiện cập nhật trạng thái booking sang 'confirmed' ở đây
    console.warn(`Xác nhận booking với ID: ${id}`)
  }

  const handleRejectAction = (id: string) => {
    // TODO: Thực hiện cập nhật trạng thái booking sang 'rejected' ở đây
    console.warn(`Từ chối booking với ID: ${id}`)
  }

  const handleViewDetailsAction = (id: string) => {
    console.warn(`Xem chi tiết booking với ID: ${id}`)
  }

  return (
    <div className="max-w-7xl mx-auto py-4 px-2 sm:px-4 md:px-8">

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Quản lý Đặt Lịch</h1>
        <Dialog open={openManualDialog} onOpenChange={setOpenManualDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => setOpenManualDialog(true)}>
              <span className="h-5 w-5 mr-2">➕</span>
              Tạo đặt lịch mới
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Tạo booking thủ công</DialogTitle>
            </DialogHeader>
            <ManualBookingForm onSubmit={handleManualBooking} onCancel={() => setOpenManualDialog(false)} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex-1 min-w-[300px]">
          <DateRangePicker onDateRangeChange={handleDateRangeChange} />
        </div>

        <ViewSelector viewMode={viewMode} onViewModeChange={handleViewModeChange} />

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Lọc theo trạng thái" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả trạng thái</SelectItem>
            {Object.entries(BOOKING_STATUSES).map(([key, { label }]) => (
              <SelectItem key={key} value={key}>{label}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="relative flex-1 min-w-[300px]">
          <span className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            🔍
          </span>
          <Input
            placeholder="Tìm kiếm theo tên, số điện thoại..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Booking Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">
              Danh sách đặt lịch (
              {filteredBookings.length}
              )
            </h2>
            <div className="text-sm text-gray-500">
              Hiển thị
              {' '}
              {Math.min(10, filteredBookings.length)}
              {' '}
              /
              {' '}
              {filteredBookings.length}
              {' '}
              đặt lịch
            </div>
          </div>
        </div>

        <BookingTable
          bookings={filteredBookings.slice(0, 10)}
          onAcceptAction={handleAcceptAction}
          onRejectAction={handleRejectAction}
          onViewDetailsAction={handleViewDetailsAction}
        />
      </div>

      {/* Pagination (simplified) */}
      {filteredBookings.length > 10 && (
        <div className="flex justify-center mt-6">
          <div className="flex space-x-1">
            <Button variant="outline" size="sm" disabled>Trước</Button>
            <Button variant="outline" size="sm" className="bg-blue-50">1</Button>
            <Button variant="outline" size="sm">2</Button>
            <Button variant="outline" size="sm">3</Button>
            <Button variant="outline" size="sm">Sau</Button>
          </div>
        </div>
      )}
    </div>
  )
}

export default ManageBookingsScreen
