import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Env } from '@/libs/Env'
import { cn } from '@/libs/utils'
import React, { useEffect, useState } from 'react'

import { useBookingPageConfigStore } from '../stores/booking-page-config'

export interface BookingPageInfo {
  name: string
  description: string
  slug: string
}

const BookingPageInfoForm: React.FC = () => {
  const { pageInfo, setPageInfo } = useBookingPageConfigStore()
  const [highlightMissing, setHighlightMissing] = useState(false)

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newInfo = { ...pageInfo, name: e.target.value }
    setPageInfo(newInfo)

    // If we're highlighting missing fields and the user starts typing, turn off the highlight
    if (highlightMissing && e.target.value) {
      setHighlightMissing(false)
    }
  }

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newInfo = { ...pageInfo, description: e.target.value }
    setPageInfo(newInfo)
  }

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Get the raw input value
    const rawValue = e.target.value ?? ''

    // Only allow lowercase a-z, numbers 0-9, and hyphens (-)
    // 1. Convert to lowercase
    // 2. Remove any characters that are not a-z, 0-9, or hyphen
    const formattedSlug = rawValue
      .replace(/ /g, '-')
      .normalize('NFD') // bỏ dấu
      .replace(/[\u0300-\u036F]/g, '') // loại bỏ ký tự dấu tiếng Việt
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // bỏ ký tự đặc biệt (nhưng giữ khoảng trắng)
      .replace(/\s+/g, '-') // khoảng trắng → dấu gạch ngang
      .replace(/-+/g, '-') // gộp nhiều dấu gạch ngang

    // Update the page info with the formatted slug
    setPageInfo({
      ...pageInfo,
      slug: formattedSlug,
    })

    // If we're highlighting missing fields and the user starts typing, turn off the highlight
    if (highlightMissing && formattedSlug) {
      setHighlightMissing(false)
    }
  }

  // Listen for custom events to highlight missing fields
  useEffect(() => {
    const handleHighlightMissing = () => {
      setHighlightMissing(true)

      // Auto-scroll to the form if needed
      const scrollTimeout = setTimeout(() => {
        document.getElementById('booking-page-info-form')?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }, 100)

      // Clean up the timeout when the component unmounts
      return () => clearTimeout(scrollTimeout)
    }

    // Add event listener for custom event
    window.addEventListener('highlight-missing-fields', handleHighlightMissing)

    // Clean up
    return () => {
      window.removeEventListener('highlight-missing-fields', handleHighlightMissing)
    }
  }, [])

  return (
    <div id="booking-page-info-form" className="p-4">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="page-name" className={highlightMissing && !pageInfo.name ? 'text-red-500 font-bold' : ''}>
            Tên trang booking *
            {highlightMissing && !pageInfo.name && (
              <span className="ml-2 text-red-500 text-sm animate-pulse">
                (Vui lòng nhập tên trang)
              </span>
            )}
          </Label>
          <Input
            id="page-name"
            placeholder="Nhập tên trang booking"
            value={pageInfo.name}
            onChange={handleNameChange}
            required
            className={cn(
              highlightMissing && !pageInfo.name
                ? 'border-red-500 ring-1 ring-red-500 animate-pulse focus:ring-red-500'
                : '',
            )}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="page-description">Mô tả</Label>
          <Textarea
            id="page-description"
            placeholder=" Mô tả ngắn gọn về dịch vụ hoặc sự kiện của bạn"
            defaultValue={pageInfo.description}
            onBlur={handleDescriptionChange}
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="page-slug" className={highlightMissing && !pageInfo.slug ? 'text-red-500 font-bold' : ''}>
            Đường dẫn truy cập *
            {highlightMissing && !pageInfo.slug && (
              <span className="ml-2 text-red-500 text-sm animate-pulse">
                (Vui lòng nhập đường dẫn)
              </span>
            )}
          </Label>
          <div className="flex items-center">
            <span className="bg-muted px-3 py-2 rounded-l-md text-muted-foreground border border-r-0">
              {Env.NEXT_PUBLIC_DOMAIN}
              /
            </span>
            <Input
              id="page-slug"
              className={cn(
                'rounded-l-none',
                highlightMissing && !pageInfo.slug
                  ? 'border-red-500 ring-1 ring-red-500 animate-pulse focus:ring-red-500'
                  : '',
              )}
              placeholder="ten-trang-booking"
              value={pageInfo.slug}
              onChange={handleSlugChange}
              required
            />
          </div>
          <p className="text-xs text-muted-foreground">
            Đường dẫn để người dùng truy cập trang booking của bạn
          </p>
        </div>
      </div>
    </div>
  )
}

export default BookingPageInfoForm
