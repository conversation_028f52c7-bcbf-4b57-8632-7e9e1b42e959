'use client'

import { useEffect, useState } from 'react'

const SIDEBAR_COOKIE_NAME = 'sidebar_state'
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7 // 7 days

/**
 * Hook to manage sidebar state with cookie persistence
 * @param defaultOpen Default state if no cookie is found
 * @returns Sidebar state and setter
 */
export function useSidebarState(defaultOpen = true) {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  // Load state from cookie on mount
  useEffect(() => {
    const cookies = document.cookie.split(';')
    const sidebarCookie = cookies.find(cookie =>
      cookie.trim().startsWith(`${SIDEBAR_COOKIE_NAME}=`),
    )

    if (sidebarCookie) {
      const value = sidebarCookie.split('=')[1]
      setIsOpen(value === 'true')
    }
  }, [])

  // Function to update state and save to cookie
  const setSidebarState = (value: boolean | ((prev: boolean) => boolean)) => {
    const newValue = typeof value === 'function' ? value(isOpen) : value
    setIsOpen(newValue)

    // Save to cookie
    document.cookie = `${SIDEBAR_COOKIE_NAME}=${newValue}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`
  }

  return [isOpen, setSidebarState] as const
}
