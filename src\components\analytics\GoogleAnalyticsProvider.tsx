'use client'

import { Env } from '@/libs/Env'
import Script from 'next/script'
import { useEffect } from 'react'
import { SuspendedGoogleAnalyticsPageView } from './GoogleAnalyticsPageView'

// Define window.gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

export const GoogleAnalyticsProvider = (props: { children: React.ReactNode }) => {
  const gaId = Env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID

  useEffect(() => {
    // Initialize gtag function
    if (gaId) {
      window.gtag = function gtag(...args: any[]) {
        window.dataLayer.push(args)
      }
      // Create dataLayer array if it doesn't exist
      window.dataLayer = window.dataLayer || []
      // Initialize gtag with default parameters
      window.gtag('js', new Date())
      window.gtag('config', gaId, {
        page_path: window.location.pathname,
      })
    }
  }, [gaId])

  if (!gaId) {
    return props.children
  }

  return (
    <>
      {/* Google Analytics Script */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
      />
      <SuspendedGoogleAnalyticsPageView />
      {props.children}
    </>
  )
}
