'use client'

import { Env } from '@/libs/Env'
import { usePathname, useSearchParams } from 'next/navigation'
import { Suspense, useEffect } from 'react'

const GoogleAnalyticsPageView = () => {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const gaId = Env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID

  // Track pageviews
  useEffect(() => {
    if (pathname && gaId && window.gtag) {
      let url = window.origin + pathname
      if (searchParams.toString()) {
        url = `${url}?${searchParams.toString()}`
      }

      window.gtag('config', gaId, {
        page_path: pathname,
        page_location: url,
      })
    }
  }, [pathname, searchParams, gaId])

  return null
}

// Wrap in Suspense to prevent hydration errors
export const SuspendedGoogleAnalyticsPageView = () => (
  <Suspense fallback={null}>
    <GoogleAnalyticsPageView />
  </Suspense>
)
