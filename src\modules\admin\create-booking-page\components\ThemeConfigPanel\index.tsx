'use client'

import type { ThemeSettings } from '../../types/theme'
import React from 'react'
import { LayoutType } from '../../types/theme'
import CollapsibleBlock from '../CollapsibleBlock'

interface ThemeConfigPanelProps {
  themeSettings: ThemeSettings
  handleColorChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleLayoutChange: (layout: LayoutType) => void
  useCollapsible?: boolean
}

/**
 * ThemeConfigPanel Component
 *
 * A reusable component for configuring theme settings across different booking page types
 */
const ThemeConfigPanel: React.FC<ThemeConfigPanelProps> = ({
  themeSettings,
  handleColorChange,
  handleLayoutChange,
  useCollapsible = false,
}) => {
  const content = (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-3"><PERSON><PERSON><PERSON> sắc chủ đạo</h3>
        <div className="flex items-center gap-3">
          <input
            type="color"
            value={themeSettings.primaryColor}
            onChange={handleColorChange}
            className="w-12 h-12 p-1 rounded"
          />
          <input
            type="text"
            value={themeSettings.primaryColor}
            onChange={handleColorChange}
            className="w-32 p-2 border rounded"
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-3">Bố cục trang</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            type="button"
            className={`border rounded-lg p-3 cursor-pointer ${themeSettings.layout === LayoutType.VERTICAL ? 'border-blue-500 bg-blue-50' : ''}`}
            onClick={() => handleLayoutChange(LayoutType.VERTICAL)}
          >
            <div className="w-full h-32 bg-gray-100 flex flex-col justify-center items-center">
              <div className="w-3/4 h-4 bg-gray-300 mb-2"></div>
              <div className="w-3/4 h-4 bg-gray-300 mb-2"></div>
              <div className="w-3/4 h-4 bg-gray-300"></div>
            </div>
            <p className="text-center mt-2 text-sm">Dọc</p>
          </button>

          <button
            type="button"
            className={`border rounded-lg p-3 cursor-pointer ${themeSettings.layout === LayoutType.TWO_COLUMN ? 'border-blue-500 bg-blue-50' : ''}`}
            onClick={() => handleLayoutChange(LayoutType.TWO_COLUMN)}
          >
            <div className="w-full h-32 bg-gray-100 flex justify-between p-2">
              <div className="w-[48%] h-full flex flex-col justify-center items-center">
                <div className="w-full h-3 bg-gray-300 mb-2"></div>
                <div className="w-full h-3 bg-gray-300"></div>
              </div>
              <div className="w-[48%] h-full flex flex-col justify-center items-center">
                <div className="w-full h-3 bg-gray-300 mb-2"></div>
                <div className="w-full h-3 bg-gray-300"></div>
              </div>
            </div>
            <p className="text-center mt-2 text-sm">2 cột</p>
          </button>

          <button
            type="button"
            className={`border rounded-lg p-3 cursor-pointer ${themeSettings.layout === LayoutType.SPORT_BASIC ? 'border-blue-500 bg-blue-50' : ''}`}
            onClick={() => handleLayoutChange(LayoutType.SPORT_BASIC)}
          >
            <div className="w-full h-32 bg-gray-100 flex flex-col p-2">
              {/* Banner row */}
              <div className="w-full h-1/3 mb-2">
                <div className="w-full h-full bg-gray-300"></div>
              </div>
              {/* Two column row */}
              <div className="w-full h-2/3 flex justify-between">
                <div className="w-[48%] h-full flex flex-col justify-center items-center">
                  <div className="w-full h-3 bg-gray-300 mb-2"></div>
                  <div className="w-full h-3 bg-gray-300"></div>
                </div>
                <div className="w-[48%] h-full flex flex-col justify-center items-center">
                  <div className="w-full h-3 bg-gray-300 mb-2"></div>
                  <div className="w-full h-3 bg-gray-300"></div>
                </div>
              </div>
            </div>
            <p className="text-center mt-2 text-sm">Banner + 2 cột</p>
          </button>

          <button
            type="button"
            className={`border rounded-lg p-3 cursor-pointer ${themeSettings.layout === LayoutType.BANNER_SPLIT_MAP ? 'border-blue-500 bg-blue-50' : ''}`}
            onClick={() => handleLayoutChange(LayoutType.BANNER_SPLIT_MAP)}
          >
            <div className="w-full h-32 bg-gray-100 flex flex-col p-2">
              {/* Banner row */}
              <div className="w-full h-1/4 mb-2">
                <div className="w-full h-full bg-gray-300"></div>
              </div>
              {/* 30-70 split row */}
              <div className="w-full h-2/4 flex justify-between mb-2">
                <div className="w-[28%] h-full flex flex-col justify-center items-center">
                  <div className="w-full h-3 bg-gray-300 mb-2"></div>
                  <div className="w-full h-3 bg-gray-300"></div>
                </div>
                <div className="w-[68%] h-full flex flex-col justify-center items-center">
                  <div className="w-full h-3 bg-gray-300 mb-2"></div>
                  <div className="w-full h-3 bg-gray-300"></div>
                </div>
              </div>
              {/* Map row */}
              <div className="w-full h-1/4">
                <div className="w-full h-full bg-gray-300"></div>
              </div>
            </div>
            <p className="text-center mt-2 text-sm">Banner + 30/70 + Map</p>
          </button>
        </div>
      </div>
    </div>
  )

  if (useCollapsible) {
    return (
      <CollapsibleBlock title="Màu sắc và bố cục" defaultOpen={true}>
        {content}
      </CollapsibleBlock>
    )
  }

  return content
}

export default ThemeConfigPanel
