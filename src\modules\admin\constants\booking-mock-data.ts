// Dữ liệu mẫu cho các booking
export type BookingStatus = 'pending' | 'confirmed' | 'rejected' | 'completed' | 'cancelled'

export type Booking = {
  id: string
  customerName: string
  customerEmail: string
  customerPhone: string
  bookingTime: string // ISO date string
  duration: number // in minutes
  status: BookingStatus
  pageId: string
  pageName: string
  notes?: string
  createdAt: string // ISO date string
}

// Trạng thái booking và màu sắc tương ứng
export const BOOKING_STATUSES: Record<BookingStatus, { label: string, color: string }> = {
  pending: { label: 'Chờ xác nhận', color: 'yellow' },
  confirmed: { label: 'Đã xác nhận', color: 'green' },
  rejected: { label: 'Từ chối', color: 'red' },
  completed: { label: 'Hoàn thành', color: 'blue' },
  cancelled: { label: 'Đã hủy', color: 'gray' },
}

// Tạo dữ liệu mẫu cho 30 ngày gần đây
const generateMockBookings = (): Booking[] => {
  const bookings: Booking[] = []
  const pageNames = [
    'Sân Bóng Đá Mini',
    'Phòng Gym Center',
    'Tư Vấn Tài <PERSON>',
    'Concert Mùa Hè 2025',
    'Dịch Vụ Đưa Đón Sân Bay',
  ]

  const pageIds = [
    'page-001',
    'page-002',
    'page-003',
    'page-004',
    'page-005',
  ]

  const statuses: BookingStatus[] = ['pending', 'confirmed', 'rejected', 'completed', 'cancelled']
  const customerNames = [
    'Nguyễn Văn A',
    'Trần Thị B',
    'Lê Văn C',
    'Phạm Thị D',
    'Hoàng Văn E',
    'Đỗ Thị F',
    'Vũ Văn G',
    'Ngô Thị H',
    'Đặng Văn I',
    'Bùi Thị K',
  ]

  const now = new Date()

  // Tạo booking cho 30 ngày gần đây
  for (let i = 0; i < 100; i++) {
    const daysAgo = Math.floor(Math.random() * 30)
    const hoursAgo = Math.floor(Math.random() * 24)
    const minutesAgo = Math.floor(Math.random() * 60)

    const bookingDate = new Date(now)
    bookingDate.setDate(bookingDate.getDate() - daysAgo)
    bookingDate.setHours(bookingDate.getHours() - hoursAgo)
    bookingDate.setMinutes(bookingDate.getMinutes() - minutesAgo)

    const createdAtDate = new Date(bookingDate)
    createdAtDate.setHours(createdAtDate.getHours() - Math.floor(Math.random() * 24))

    const pageIndex = Math.floor(Math.random() * pageNames.length)
    const customerIndex = Math.floor(Math.random() * customerNames.length)
    const statusIndex = Math.floor(Math.random() * statuses.length)
    const duration = [30, 60, 90, 120][Math.floor(Math.random() * 4)]

    bookings.push({
      id: `booking-${i + 1}`,
      customerName: customerNames[customerIndex] || '',
      customerEmail: `${(customerNames[customerIndex] || '').toLowerCase().replace(/ /g, '.')}@example.com`,
      customerPhone: `09${Math.floor(10000000 + Math.random() * 90000000)}`,
      bookingTime: bookingDate.toISOString(),
      duration: duration || 30,
      status: statuses[statusIndex] || 'pending',
      pageId: pageIds[pageIndex] || '',
      pageName: pageNames[pageIndex] || '',
      notes: Math.random() > 0.7 ? 'Ghi chú từ khách hàng' : undefined,
      createdAt: createdAtDate.toISOString(),
    })
  }

  // Sắp xếp theo thời gian booking mới nhất trước
  return bookings.sort((a, b) => new Date(b.bookingTime).getTime() - new Date(a.bookingTime).getTime())
}

export const MOCK_BOOKINGS = generateMockBookings()
