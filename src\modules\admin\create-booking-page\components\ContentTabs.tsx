import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import React from 'react'
import BookingPageReview from './BookingPageReview'
import QuickConfig, { QuickConfigPanel } from './QuickConfig'

interface ContentTabsProps {
  activeTab: 'config' | 'preview'
  onTabChange: (value: 'config' | 'preview') => void
}

const ContentTabs: React.FC<ContentTabsProps> = ({ activeTab, onTabChange }) => {
  return (
    <div className="w-full relative">
      <Tabs value={activeTab} onValueChange={value => onTabChange(value as 'config' | 'preview')}>
        <TabsList className="mb-4">
          <TabsTrigger value="preview">Xem trước</TabsTrigger>
          <TabsTrigger value="config">C<PERSON>u hình</TabsTrigger>
        </TabsList>

        <TabsContent value="config" className="bg-white rounded-lg shadow p-6">
          <QuickConfigPanel />
        </TabsContent>

        <TabsContent
          value="preview"
          className="bg-white rounded-lg shadow p-6 transition-all duration-300"
        >
          <div className="relative">
            <BookingPageReview />
            <QuickConfig />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ContentTabs
