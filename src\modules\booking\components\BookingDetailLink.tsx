import { Button } from '@/components/ui/button'
import { appPaths } from '@/utils/app-routes'
import { ExternalLink } from 'lucide-react'

interface BookingDetailLinkProps {
  bookingId: string
  children?: React.ReactNode
  className?: string
  variant?: 'default' | 'outline' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg'
  asButton?: boolean
}

/**
 * Component to create links to booking detail page
 * Can be used in emails, notifications, or anywhere we need to link to booking details
 */
export const BookingDetailLink: React.FC<BookingDetailLinkProps> = ({
  bookingId,
  children,
  className,
  variant = 'default',
  size = 'default',
  asButton = true,
}) => {
  const bookingDetailUrl = appPaths.public.bookingDetail(bookingId)

  if (asButton) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => window.open(bookingDetailUrl, '_blank')}
      >
        {children || (
          <>
            <ExternalLink className="w-4 h-4 mr-2" />
            Xem chi tiết đặt chỗ
          </>
        )}
      </Button>
    )
  }

  return (
    <a
      href={bookingDetailUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children || 'Xem chi tiết đặt chỗ'}
    </a>
  )
}

/**
 * Hook to get booking detail URL
 */
export const useBookingDetailUrl = (bookingId: string) => {
  const bookingDetailUrl = appPaths.public.bookingDetail(bookingId)
  const fullUrl = typeof window !== 'undefined'
    ? `${window.location.origin}${bookingDetailUrl}`
    : bookingDetailUrl

  return {
    path: bookingDetailUrl,
    fullUrl,
  }
}

export default BookingDetailLink
