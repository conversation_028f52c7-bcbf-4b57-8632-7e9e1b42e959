import type { BookedSlot } from '@/modules/booking/booking.apis'
import { beforeEach, describe, expect, it } from 'vitest'
import { useBookingSlotsStore } from './booking-slots.store'

describe('BookingSlotsStore Performance Tests', () => {
  beforeEach(() => {
    // Reset the store before each test
    useBookingSlotsStore.getState().clearBookedSlots()
  })

  it('should demonstrate performance improvement with large dataset', () => {
    const store = useBookingSlotsStore.getState()

    // Create a large dataset of booked slots (1000 slots)
    const largeBookedSlots: BookedSlot[] = []
    for (let i = 0; i < 1000; i++) {
      largeBookedSlots.push({
        field: `field-${i % 10}`, // 10 different fields
        time: `${8 + (i % 12)}:00`, // Times from 8:00 to 19:00
        date: '2024-01-15',
        bookingId: `booking-${i}`,
        status: i % 3 === 0 ? 'confirmed' : i % 3 === 1 ? 'pending' : 'cancelled',
      })
    }

    // Set the large dataset
    useBookingSlotsStore.setState({
      bookedSlots: largeBookedSlots,
    })

    // Rebuild lookup maps
    store.rebuildLookupMaps()

    // Test performance of slot checking
    const startTime = performance.now()

    // Check 1000 different slots (this would be O(n*m) with the old approach)
    for (let i = 0; i < 1000; i++) {
      const field = `field-${i % 10}`
      const time = `${8 + (i % 12)}:00`

      // These operations should be O(1) with the optimized approach
      store.isSlotBooked(field, time)
      store.getSlotStatus(field, time)
    }

    const endTime = performance.now()
    const executionTime = endTime - startTime

    // With the optimized approach, this should be very fast (< 10ms)
    // With the old approach, this would be much slower (potentially 100ms+)
    // Execution time should be under 10ms with the optimized lookup

    // The test should complete quickly
    expect(executionTime).toBeLessThan(10) // Should be much faster with O(1) lookup
  })

  it('should correctly handle slot status with optimized lookup', () => {
    const store = useBookingSlotsStore.getState()

    // Set test data
    useBookingSlotsStore.setState({
      bookedSlots: [
        {
          field: 'field-1',
          time: '10:00',
          date: '2024-01-15',
          bookingId: 'booking-1',
          status: 'confirmed',
        },
        {
          field: 'field-1',
          time: '11:00',
          date: '2024-01-15',
          bookingId: 'booking-2',
          status: 'pending',
        },
        {
          field: 'field-1',
          time: '12:00',
          date: '2024-01-15',
          bookingId: 'booking-3',
          status: 'cancelled',
        },
      ],
    })

    // Rebuild lookup maps
    store.rebuildLookupMaps()

    // Test all scenarios
    expect(store.isSlotBooked('field-1', '10:00')).toBe(true) // confirmed
    expect(store.isSlotBooked('field-1', '11:00')).toBe(true) // pending
    expect(store.isSlotBooked('field-1', '12:00')).toBe(false) // cancelled
    expect(store.isSlotBooked('field-1', '13:00')).toBe(false) // not found

    expect(store.getSlotStatus('field-1', '10:00')).toBe('confirmed')
    expect(store.getSlotStatus('field-1', '11:00')).toBe('pending')
    expect(store.getSlotStatus('field-1', '12:00')).toBe('available') // cancelled = available
    expect(store.getSlotStatus('field-1', '13:00')).toBe('available') // not found = available
  })

  it('should maintain consistency between array and lookup map', () => {
    const store = useBookingSlotsStore.getState()

    const testSlots: BookedSlot[] = [
      {
        field: 'field-A',
        time: '09:00',
        date: '2024-01-15',
        bookingId: 'booking-A',
        status: 'confirmed',
      },
      {
        field: 'field-B',
        time: '10:00',
        date: '2024-01-15',
        bookingId: 'booking-B',
        status: 'pending',
      },
    ]

    // Set test data
    useBookingSlotsStore.setState({
      bookedSlots: testSlots,
    })

    // Rebuild lookup maps
    store.rebuildLookupMaps()

    // Verify that the lookup map contains the same data as the array
    const state = useBookingSlotsStore.getState()

    testSlots.forEach((slot) => {
      const key = `${slot.field}:${slot.time}`
      const lookupSlot = state.slotLookupMap.get(key)

      expect(lookupSlot).toBeDefined()
      expect(lookupSlot?.field).toBe(slot.field)
      expect(lookupSlot?.time).toBe(slot.time)
      expect(lookupSlot?.status).toBe(slot.status)
      expect(lookupSlot?.bookingId).toBe(slot.bookingId)
    })

    // Verify lookup map size matches array length
    expect(state.slotLookupMap.size).toBe(testSlots.length)
  })
})
