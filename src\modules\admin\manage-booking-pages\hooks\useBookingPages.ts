import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

/**
 * Custom hook to fetch and manage booking pages
 */
export const useBookingPages = () => {
  const [bookingPages, setBookingPages] = useState<BookingPageItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5 // Number of items per page

  // Fetch booking pages from API
  const fetchBookingPages = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await bookingPageAPIs.getBookingPages()

      // Handle the response data safely
      if (response && response.data) {
        const bookingPagesData = Array.isArray(response.data)
          ? response.data as BookingPageItem[]
          : [response.data as BookingPageItem]
        setBookingPages(bookingPagesData)
      } else {
        setBookingPages([])
      }
    } catch (err) {
      console.error('Failed to fetch booking pages:', err)
      setError('Không thể tải danh sách booking pages. Vui lòng thử lại sau.')
      toast.error('Không thể tải danh sách booking pages')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load booking pages on mount
  useEffect(() => {
    fetchBookingPages()
  }, [fetchBookingPages])

  // Reset to first page when filters change
  useEffect(() => {
    if (searchQuery !== '' || statusFilter !== 'all') {
      setCurrentPage(1)
    }
  }, [searchQuery, statusFilter])

  // Filter booking pages based on search query and status filter
  const filteredPages = bookingPages.filter((page) => {
    const matchesSearch = page.name.toLowerCase().includes(searchQuery.toLowerCase())
      || page.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || page.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredPages.length / itemsPerPage)
  const paginatedPages = filteredPages.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  )

  return {
    bookingPages: paginatedPages,
    isLoading,
    error,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    currentPage,
    setCurrentPage,
    totalPages,
    fetchBookingPages,
    hasFilters: searchQuery !== '' || statusFilter !== 'all',
  }
}

export default useBookingPages
