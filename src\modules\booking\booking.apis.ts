import type { BookingPageItem } from '../admin/apis/booking-page.api'
import publicApi from '@/apis/publicApi'

export interface GetBookedSlotsParams {
  bookingPageId: string
  date: string // Format: YYYY-MM-DD
}

export interface CreateBookingPayload {
  slug: string
  bookingPageId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  bookingDate: string
  bookingSlots: {
    date: string | Date
    field: string
    time: string
  }[]
  paymentMethod: string
  quantity?: number
  notes?: string
}

export interface BookingResponse {
  id: string
  status: 'pending' | 'confirmed' | 'cancelled'
  message?: string
}

// Types for booking slots
export interface BookedSlot {
  field: string
  time: string
  date: string
  bookingId: string
  status: 'pending' | 'confirmed' | 'cancelled'
}

export interface BookedSlotsData {
  bookingPageId: string
  date: string
  bookedSlots: BookedSlot[]
}

export const bookingAPIs = {
  // Use publicApi for public endpoints that don't require authentication
  getBookingPageBySlug: (slug: string) =>
    publicApi.get<BookingPageItem>(`/public/booking-page/slug/${slug}`),

  // Create a new booking
  createBooking: (data: CreateBookingPayload) =>
    publicApi.post<BookingResponse>('/public/booking', data),

  getBookedSlots: (params: GetBookedSlotsParams) => publicApi.get<BookedSlotsData>(`/public/booking/slots/booked`, {
    params,
  }),
}
