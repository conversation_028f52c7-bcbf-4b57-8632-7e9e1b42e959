import { Button } from '@/components/ui/button'
import { cn } from '@/libs/utils'
import { ChevronRight, Settings2 } from 'lucide-react'
import React, { useImperativeHandle, useState } from 'react'
import { TemplateCategory } from '../constants/templates'

import { useBookingPageConfigStore } from '../stores/booking-page-config'
import { quickConfigRef } from '../utils/quick-config-helper'
// Import all template config components
import BlockBasedTemplateConfig from './BookingPageConfig/BlockBasedTemplateConfig'
import ServiceConfig from './BookingPageConfig/ServiceConfig'
import SportFieldConfig from './BookingPageConfig/SportFieldConfig'
import TransportationConfig from './BookingPageConfig/TransportationConfig'

// Quick Config Panel Component
export const QuickConfigPanel = () => {
  const { templateSelected } = useBookingPageConfigStore()

  // Render different config options based on template category or code
  const renderConfigOptions = () => {
    // First check specific template codes
    switch (templateSelected.templateCode) {
      case 'EVENT_BOOKING':
      case 'CONFERENCE_BOOKING':
        return <BlockBasedTemplateConfig />
    }

    // Then check by category
    switch (templateSelected.category) {
      case TemplateCategory.EVENT:
        return <BlockBasedTemplateConfig />

      case TemplateCategory.SPORT:
        return <SportFieldConfig />

      case TemplateCategory.SERVICE:
        return <ServiceConfig />

      case TemplateCategory.TRANSPORTATION:
        return <TransportationConfig />

      default:
        return (
          <div className="p-4 border rounded-lg bg-gray-50">
            <p className="text-center text-gray-500">
              Chưa có cấu hình cho loại template này
            </p>
          </div>
        )
    }
  }

  return (
    <div className="space-y-6">
      {renderConfigOptions()}
    </div>
  )
}

// Convert to forwardRef component
const QuickConfig = () => {
  const [isQuickConfigOpen, setIsQuickConfigOpen] = useState(false)

  // Expose methods through the ref
  useImperativeHandle(quickConfigRef, () => ({
    open: () => setIsQuickConfigOpen(true),
    close: () => setIsQuickConfigOpen(false),
    toggle: () => setIsQuickConfigOpen(prev => !prev),
  }))

  return (
    <>
      <Button
        variant="outline"
        size="lg"
        className="fixed right-4 top-1/2 transform -translate-y-1/2 z-20"
        onClick={() => setIsQuickConfigOpen(!isQuickConfigOpen)}
      >
        {isQuickConfigOpen ? <ChevronRight /> : <Settings2 />}
      </Button>
      <div
        className={cn(
          'fixed right-0 top-0 h-full w-full max-w-[40rem] transform transition-transform duration-300 z-10',
          isQuickConfigOpen ? 'translate-x-0' : 'translate-x-full',
        )}
      >
        <div className="p-4 h-dvh overflow-y-auto bg-white shadow-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">Cấu hình nhanh</h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsQuickConfigOpen(!isQuickConfigOpen)}
            >
              <ChevronRight />
            </Button>
          </div>
          <QuickConfigPanel />
        </div>
      </div>
    </>
  )
}

// Add display name for better debugging
QuickConfig.displayName = 'QuickConfig'

export default QuickConfig
