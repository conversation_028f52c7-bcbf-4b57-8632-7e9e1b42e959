# Booking Slots Integration

## Tổng quan

Tính năng này tích hợp việc tự động tải thông tin các slot đã được đặt khi người dùng chọn ngày trong `AvailabilityCalendarBlockComponent`. <PERSON>hi `selectedDate` thay đổi, hệ thống sẽ tự động gọi API `bookingAPIs.getBookedSlots` và cập nhật store `useBookingSlotsStore`.

## Cách hoạt động

### 1. Store Management

`useBookingSlotsStore` quản lý:
- `bookedSlots`: Danh sách các slot đã được đặt
- `selectedDate`: Ng<PERSON>y được chọn (format: YYYY-MM-DD)
- `bookingPageId`: ID của booking page
- `isLoading`: Trạng thái loading
- `error`: Thông báo lỗi

### 2. Actions

#### `setBookingPageId(id: string)`
- Set booking page ID
- Nếu đã có `selectedDate`, tự động load booked slots

#### `setSelectedDate(date: string)`
- Set ngày được chọn (không tự động load slots)

#### `setSelectedDateAndLoadSlots(date: string)`
- Set ngày được chọn và tự động load booked slots nếu có `bookingPageId`

#### `setSelectedDateFromDateObject(date: Date)`
- Set ngày từ Date object và tự động load booked slots
- Tự động convert Date thành string format YYYY-MM-DD

#### `loadBookedSlots(bookingPageId: string, date: string)`
- Load danh sách booked slots từ API
- Cập nhật state với dữ liệu mới

### 3. Component Integration

#### AvailabilityCalendarBlockComponent

Component này đã được cập nhật để:
- Nhận prop `bookingPageId`
- Tự động set booking page ID khi component mount
- Gọi `setSelectedDateFromDateObject` khi user chọn ngày

```tsx
<AvailabilityCalendarBlockComponent
  title="Đặt sân"
  bookingPageId="booking-page-123"
  // ... other props
/>
```

#### BlockRenderer

`BlockRenderer` đã được cập nhật để:
- Nhận prop `bookingPageId`
- Truyền `bookingPageId` xuống các block components

#### PublicBookingPage

`PublicBookingPage` truyền `bookingPageId` từ `bookingPage._id` xuống `BlockRenderer`.

## API Integration

### Endpoint
```
GET /public/booking/slots/booked
```

### Parameters
```typescript
{
  bookingPageId: string
  date: string // YYYY-MM-DD format
}
```

### Response
```typescript
{
  status: { success: boolean, message?: string }
  data: {
    bookingPageId: string
    date: string
    bookedSlots: BookedSlot[]
  }
}
```

### BookedSlot Type
```typescript
interface BookedSlot {
  field: string
  time: string
  date: string
  bookingId: string
  status: 'pending' | 'confirmed' | 'cancelled'
}
```

## Usage Examples

### 1. Kiểm tra slot đã được đặt
```typescript
const { isSlotBooked } = useBookingSlotsStore()

const isBooked = isSlotBooked('field-1', '10:00')
// Returns true if slot is booked (status !== 'cancelled')
```

### 2. Lấy trạng thái slot
```typescript
const { getSlotStatus } = useBookingSlotsStore()

const status = getSlotStatus('field-1', '10:00')
// Returns: 'available' | 'pending' | 'confirmed' | 'cancelled'
```

### 3. Set ngày và tự động load slots
```typescript
const { setSelectedDateFromDateObject } = useBookingSlotsStore()

// Từ Date object
await setSelectedDateFromDateObject(new Date('2024-01-15'))

// Từ string
await setSelectedDateAndLoadSlots('2024-01-15')
```

### 4. Reload slots sau khi booking thành công
```typescript
const { reloadCurrentSlots } = useBookingSlotsStore()

// Reload slots hiện tại (sử dụng bookingPageId và selectedDate đã có)
await reloadCurrentSlots()

// Thường được gọi sau khi submit booking thành công
try {
  await bookingAPIs.createBooking(bookingData)
  // Booking thành công, reload để cập nhật trạng thái slots
  await reloadCurrentSlots()
} catch (error) {
  // Handle error
}
```

## Error Handling

Store tự động xử lý lỗi và cập nhật `error` state. Component có thể hiển thị thông báo lỗi:

```tsx
const { error, isLoading } = useBookingSlotsStore()

{ error && (
  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
    <p className="text-red-700 text-sm">
      Lỗi khi tải thông tin slot:
      {' '}
      {error}
    </p>
  </div>
) }

{ isLoading && (
  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <p className="text-blue-700 text-sm flex items-center gap-2">
      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700"></div>
      Đang tải thông tin slot...
    </p>
  </div>
) }
```

## Timezone Handling

### Vấn đề Timezone

Khi sử dụng `date.toISOString().split('T')[0]`, Date object sẽ được convert sang UTC timezone trước khi format. Điều này có thể gây ra việc ngày bị thay đổi:

```javascript
// Ví dụ: User chọn ngày 23, nhưng kết quả lại là 22
const date = new Date(2024, 0, 23) // January 23, 2024 local time
const wrongFormat = date.toISOString().split('T')[0] // Có thể thành "2024-01-22" nếu ở timezone +7
```

### Giải pháp

Sử dụng local timezone methods để format date:

```javascript
const formatDateToString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return date
  }

  // Use local timezone instead of UTC to avoid date shifting
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}
```

## Testing

Sử dụng file test `booking-slots.store.test.ts` để kiểm tra các tính năng:

```bash
npm test booking-slots.store.test.ts
```

Test cases bao gồm:
- Set booking page ID và auto-load slots
- Set selected date và auto-load slots
- Convert Date object thành string format (với timezone handling)
- Kiểm tra slot booking status
- Lấy slot status
- Test timezone issues không xảy ra
