'use client'

import type { Booking } from '@/modules/admin/constants/booking-mock-data'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { BOOKING_STATUSES } from '@/modules/admin/constants/booking-mock-data'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'

import React from 'react'

type BookingTableProps = {
  bookings: Booking[]
  onAcceptAction: (id: string) => void
  onRejectAction: (id: string) => void
  onViewDetailsAction: (id: string) => void
}

export const BookingTable = ({
  bookings,
  onAcceptAction,
  onRejectAction,
  onViewDetailsAction,
}: BookingTableProps) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, 'HH:mm - dd/MM/yyyy', { locale: vi })
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} phút`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    if (remainingMinutes === 0) {
      return `${hours} giờ`
    }
    return `${hours} giờ ${remainingMinutes} phút`
  }

  const getStatusBadge = (status: string) => {
    const statusInfo = BOOKING_STATUSES[status as keyof typeof BOOKING_STATUSES]
    return (
      <Badge
        variant="outline"
        className={`bg-${statusInfo.color}-50 text-${statusInfo.color}-700 border-${statusInfo.color}-200`}
      >
        {statusInfo.label}
      </Badge>
    )
  }

  const [confirmAction, setConfirmAction] = React.useState<'accept' | 'reject' | null>(null)
  const [selectedBookingId, setSelectedBookingId] = React.useState<string | null>(null)

  const handleOpenConfirm = (action: 'accept' | 'reject', id: string) => {
    setConfirmAction(action)
    setSelectedBookingId(id)
  }

  const handleCloseConfirm = () => {
    setConfirmAction(null)
    setSelectedBookingId(null)
  }

  const handleConfirm = () => {
    if (confirmAction && selectedBookingId) {
      if (confirmAction === 'accept') {
        onAcceptAction(selectedBookingId)
      }
      if (confirmAction === 'reject') {
        onRejectAction(selectedBookingId)
      }
    }
    handleCloseConfirm()
  }

  return (
    <div className="rounded-md border overflow-x-auto">

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Tên khách hàng</TableHead>
            <TableHead>Thời gian đặt</TableHead>
            <TableHead>Thời lượng</TableHead>
            <TableHead>Dịch vụ</TableHead>
            <TableHead>Trạng thái</TableHead>
            <TableHead className="text-right">Hành động</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {bookings.length > 0
            ? (
                bookings.map(booking => (
                  <TableRow key={booking.id}>
                    <TableCell className="font-medium">
                      <div>{booking.customerName}</div>
                      <div className="text-xs text-gray-500">{booking.customerPhone}</div>
                    </TableCell>
                    <TableCell>{formatDate(booking.bookingTime)}</TableCell>
                    <TableCell>{formatDuration(booking.duration)}</TableCell>
                    <TableCell>{booking.pageName}</TableCell>
                    <TableCell>{getStatusBadge(booking.status)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        {booking.status === 'pending' && (
                          <>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-8 border-green-500 text-green-600 hover:bg-green-50"
                                  onClick={() => handleOpenConfirm('accept', booking.id)}
                                >
                                  Xác nhận
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Xác nhận booking?</AlertDialogTitle>
                                  <AlertDialogDescription>Bạn có chắc chắn muốn xác nhận booking này?</AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Huỷ</AlertDialogCancel>
                                  <AlertDialogAction onClick={handleConfirm}>Đồng ý</AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-8 border-red-500 text-red-600 hover:bg-red-50"
                                  onClick={() => handleOpenConfirm('reject', booking.id)}
                                >
                                  Từ chối
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Từ chối booking?</AlertDialogTitle>
                                  <AlertDialogDescription>Bạn có chắc chắn muốn từ chối booking này?</AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Huỷ</AlertDialogCancel>
                                  <AlertDialogAction onClick={handleConfirm}>Đồng ý</AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="icon" variant="ghost" className="h-8 w-8">
                              <span className="h-4 w-4">⋮</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onViewDetailsAction(booking.id)}>
                              <span className="h-4 w-4 mr-2">📋</span>
                              Xem chi tiết
                            </DropdownMenuItem>
                            {booking.status === 'pending' && (
                              <>
                                <DropdownMenuItem onClick={() => onAcceptAction(booking.id)}>
                                  <span className="h-4 w-4 mr-2">✅</span>
                                  Xác nhận
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => onRejectAction(booking.id)}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <span className="h-4 w-4 mr-2">❌</span>
                                  Từ chối
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )
            : (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    Không có booking nào.
                  </TableCell>
                </TableRow>
              )}
        </TableBody>
      </Table>
    </div>
  )
}
