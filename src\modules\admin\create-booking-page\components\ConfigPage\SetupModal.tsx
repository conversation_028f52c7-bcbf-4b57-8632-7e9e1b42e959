import type { SetupState } from '../../types/types'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { cn } from '@/libs/utils'
import { Check, CheckCircle2, Info } from 'lucide-react'
import React, { memo, useCallback } from 'react'
import { BOOKING_PAGE_TEMPLATES } from '../../constants/templates'
import { useBookingPageConfigStore } from '../../stores/booking-page-config'
import BookingPageInfoForm from '../BookingPageInfoForm'
import TemplateSelectorPanel from '../TemplateSelectorPanel'

interface SetupModalProps {
  state: SetupState
  onStateChange: (updates: Partial<SetupState>) => void
  selectedTemplate: any
  handleTemplateSelect: (id: string) => void
  onValidate: () => boolean
  isEditMode?: boolean
}

/**
 * SetupModal Component
 * Modal for configuring the booking page with a side-by-side layout
 * for better usability and reduced clicking
 */
const SetupModal = memo(({
  state,
  onStateChange,
  selectedTemplate,
  handleTemplateSelect,
  onValidate,
  isEditMode = false,
}: SetupModalProps) => {
  const { pageInfo } = useBookingPageConfigStore()
  // Compute template validation status directly from props
  const hasTemplate = !!selectedTemplate?.id

  // Local state to track info validation status
  // const [hasValidInfo] = useState(false)
  const hasValidInfo = !!pageInfo.name && !!pageInfo.slug

  // Combined validation status object
  const validationStatus = {
    info: hasValidInfo,
    template: hasTemplate,
  }

  // Handle finish setup
  const handleFinishSetup = useCallback(() => {
    if (onValidate()) {
      onStateChange({ isModalOpen: false })
    }
  }, [onValidate, onStateChange])

  return (
    <Dialog
      open={state.isModalOpen}
      onOpenChange={open => onStateChange({ isModalOpen: open })}
    >
      <DialogContent className="sm:max-w-[95vw] max-w-[95vw] w-full lg:w-[90vw] xl:w-[1200px] max-h-[95vh] h-[800px] overflow-hidden flex flex-col p-0">
        <DialogHeader className="pb-4 border-b px-6 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl">
                {isEditMode ? 'Chỉnh sửa trang booking' : 'Thiết lập trang booking'}
              </DialogTitle>
              <DialogDescription className="mt-1">
                Cấu hình thông tin và giao diện cho trang booking của bạn
              </DialogDescription>
            </div>
            <div className="flex items-center gap-3">
              <div className={cn(
                'px-3 py-1.5 rounded-full text-sm font-medium flex items-center',
                validationStatus.info && validationStatus.template
                  ? 'bg-green-100 text-green-700'
                  : 'bg-amber-100 text-amber-700',
              )}
              >
                {validationStatus.info && validationStatus.template
                  ? (
                      <>
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        {isEditMode ? 'Sẵn sàng cập nhật' : 'Sẵn sàng tạo trang'}
                      </>
                    )
                  : (
                      <>
                        <Info className="h-4 w-4 mr-2" />
                        Đang thiết lập
                      </>
                    )}
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto p-6">
          {/* Responsive layout: 1 column on mobile, 2 columns on desktop */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Thông tin trang */}
            <div className="flex flex-col">
              <div className="flex items-center gap-3 mb-2">
                <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm">1</span>
                <h3 className="text-xl font-medium">Thông tin trang</h3>
                <Badge variant={validationStatus.info ? 'default' : 'outline'} className="ml-auto">
                  {validationStatus.info ? 'Đã hoàn thành' : 'Chưa hoàn thành'}
                </Badge>
              </div>
              <Card className="shadow-sm flex-1">
                <CardContent className="p-6">
                  <BookingPageInfoForm />
                </CardContent>
              </Card>
            </div>

            {/* Chọn giao diện */}
            <div className="flex flex-col">
              <div className="flex items-center gap-3 mb-2 lg:mt-0 mt-6">
                <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm">2</span>
                <h3 className="text-xl font-medium">Chọn giao diện</h3>
                <Badge variant={validationStatus.template ? 'default' : 'outline'} className="ml-auto">
                  {validationStatus.template ? 'Đã hoàn thành' : 'Chưa hoàn thành'}
                </Badge>
              </div>
              <Card className="shadow-sm flex-1">
                <CardContent className="p-6">
                  <TemplateSelectorPanel
                    templates={BOOKING_PAGE_TEMPLATES}
                    selectedTemplate={selectedTemplate}
                    selectedTemplateId={selectedTemplate?.id || ''}
                    onSelect={handleTemplateSelect}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <DialogFooter className="px-6 py-4 border-t flex items-center justify-between bg-muted/30">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1.5">
              <div className={cn(
                'w-3 h-3 rounded-full',
                validationStatus.info ? 'bg-green-500' : 'bg-gray-300',
              )}
              >
              </div>
              <span className={cn(
                'text-sm',
                validationStatus.info ? 'text-green-700 font-medium' : 'text-gray-500',
              )}
              >
                Thông tin
              </span>
            </div>
            <div className="flex items-center gap-1.5">
              <div className={cn(
                'w-3 h-3 rounded-full',
                validationStatus.template ? 'bg-green-500' : 'bg-gray-300',
              )}
              >
              </div>
              <span className={cn(
                'text-sm',
                validationStatus.template ? 'text-green-700 font-medium' : 'text-gray-500',
              )}
              >
                Giao diện
              </span>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-sm text-muted-foreground">
              {!validationStatus.info && !validationStatus.template
                ? 'Hoàn thành cả hai bước để tiếp tục'
                : !validationStatus.info
                    ? 'Vui lòng điền thông tin trang'
                    : !validationStatus.template
                        ? 'Vui lòng chọn giao diện'
                        : isEditMode
                          ? 'Đã sẵn sàng để cập nhật trang booking'
                          : 'Đã sẵn sàng để tạo trang booking'}
            </span>
            <Button
              onClick={handleFinishSetup}
              size="lg"
              className={cn(
                'flex items-center gap-2 px-6',
                (!validationStatus.info || !validationStatus.template) && 'opacity-70',
              )}
              disabled={!validationStatus.info || !validationStatus.template}
            >
              Hoàn tất thiết lập
              <Check className="h-5 w-5" />
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
})

SetupModal.displayName = 'SetupModal'

export default SetupModal
