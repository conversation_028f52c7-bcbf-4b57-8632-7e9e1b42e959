// Mock data cho các booking page đã tạo
export const MOCK_BOOKING_PAGES = [
  {
    id: 'page-001',
    name: '<PERSON><PERSON> Bóng Đá Mini',
    status: 'active',
    template: 'sport-field-continuous',
    createdAt: '2025-03-15T08:30:00Z',
    description: 'Đặt sân bóng đá mini với các khung giờ linh hoạt',
    avatar: 'https://placehold.co/100x100?text=Soccer',
    views: 245,
    bookings: 32,
  },
  {
    id: 'page-002',
    name: 'Phòng Gym Center',
    status: 'active',
    template: 'sport-field-slots',
    createdAt: '2025-03-18T10:15:00Z',
    description: 'Đặt lịch tập gym theo slot thời gian',
    avatar: 'https://placehold.co/100x100?text=Gym',
    views: 189,
    bookings: 27,
  },
  {
    id: 'page-003',
    name: '<PERSON><PERSON>ấ<PERSON>',
    status: 'inactive',
    template: 'simple-calendar',
    createdAt: '2025-03-20T14:45:00Z',
    description: 'Đặt lịch tư vấn tài chính cá nhân',
    avatar: 'https://placehold.co/100x100?text=Finance',
    views: 78,
    bookings: 12,
  },
  {
    id: 'page-004',
    name: 'Concert Mùa Hè 2025',
    status: 'active',
    template: 'event-booking',
    createdAt: '2025-03-25T09:00:00Z',
    description: 'Đặt vé cho concert mùa hè 2025',
    avatar: 'https://placehold.co/100x100?text=Concert',
    views: 567,
    bookings: 123,
  },
  {
    id: 'page-005',
    name: 'Dịch Vụ Đưa Đón Sân Bay',
    status: 'pending',
    template: 'car-booking',
    createdAt: '2025-04-01T11:30:00Z',
    description: 'Đặt xe đưa đón sân bay',
    avatar: 'https://placehold.co/100x100?text=Airport',
    views: 112,
    bookings: 18,
  },
]

// Các trạng thái có thể có của booking page
export const PAGE_STATUSES = {
  active: { label: 'Hoạt động', color: 'green' },
  inactive: { label: 'Tạm ngưng', color: 'gray' },
  pending: { label: 'Chờ duyệt', color: 'yellow' },
}

// Mapping template ID với tên hiển thị
export const TEMPLATE_NAMES = {
  'sport-field-continuous': 'Đặt sân thể thao - Giờ liên tục',
  'sport-field-slots': 'Đặt sân thể thao - Theo khung giờ',
  'sport-field-banner-2column': 'Đặt sân thể thao - Banner + 2 cột',
  'sport-field-split': 'Đặt sân thể thao - Banner/30-70/Map',
  'simple-calendar': 'Đặt lịch đơn giản',
  'event-booking': 'Đặt chỗ sự kiện',
  'car-booking': 'Đặt xe - Vị trí xe',
}

// Mapping template ID với template code
export const TEMPLATE_CODES = {
  'sport-field-continuous': 'SPORT_FIELD_CONTINUOUS',
  'sport-field-slots': 'SPORT_FIELD_SLOTS',
  'sport-field-banner-2column': 'SPORT_FIELD_BANNER_2COLUMN',
  'sport-field-split': 'SPORT_FIELD_SPLIT',
  'simple-calendar': 'SIMPLE_CALENDAR',
  'event-booking': 'EVENT_BOOKING',
  'car-booking': 'CAR_BOOKING',
}
