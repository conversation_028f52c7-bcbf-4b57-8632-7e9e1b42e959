import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import React, { useState } from 'react'

export type BookingPageConfig = {
  name: string
  description: string
  avatarUrl: string
}

type BookingPageConfigFormProps = {
  initialConfig?: BookingPageConfig
  onSubmit: (data: BookingPageConfig) => void
}

export const BookingPageConfigForm: React.FC<BookingPageConfigFormProps> = ({ initialConfig, onSubmit }) => {
  const [name, setName] = useState(initialConfig?.name || '')
  const [description, setDescription] = useState(initialConfig?.description || '')
  const [avatarUrl, setAvatarUrl] = useState(initialConfig?.avatarUrl || '')
  const [previewUrl, setPreviewUrl] = useState(initialConfig?.avatarUrl || '')

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const url = URL.createObjectURL(file)
      setAvatarUrl(url)
      setPreviewUrl(url)
    }
  }

  const handleAvatarUrlInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAvatarUrl(e.target.value)
    setPreviewUrl(e.target.value)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ name, description, avatarUrl: previewUrl })
  }

  return (
    <div className="flex flex-col md:flex-row gap-6">
      <form className="flex-1 space-y-4" onSubmit={handleSubmit}>
        <div>
          <label className="block font-medium mb-1">Tên trang booking *</label>
          <Input value={name} onChange={e => setName(e.target.value)} required maxLength={50} />
        </div>
        <div>
          <label className="block font-medium mb-1">Mô tả trang</label>
          <Textarea value={description} onChange={e => setDescription(e.target.value)} maxLength={200} />
        </div>
        <div>
          <label className="block font-medium mb-1">Ảnh đại diện</label>
          <Input type="file" accept="image/*" onChange={handleAvatarChange} />
          <div className="text-xs text-gray-500 mt-1">Hoặc nhập URL ảnh:</div>
          <Input value={avatarUrl} onChange={handleAvatarUrlInput} placeholder="https://..." className="mt-1" />
        </div>
        <Button type="submit" className="w-full md:w-auto">Lưu cấu hình</Button>
      </form>
      <div className="flex-1 max-w-xs mx-auto md:mx-0">
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="font-semibold mb-2">Preview</div>
          <div className="flex flex-col items-center gap-2">
            <img
              src={previewUrl || '/default-avatar.png'}
              alt="avatar preview"
              className="w-24 h-24 rounded-full object-cover border"
              onError={e => (e.currentTarget.src = '/default-avatar.png')}
            />
            <div className="font-bold text-lg text-center">{name || 'Tên trang booking'}</div>
            <div className="text-gray-700 text-center text-sm">{description || 'Mô tả trang sẽ hiển thị ở đây.'}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingPageConfigForm
