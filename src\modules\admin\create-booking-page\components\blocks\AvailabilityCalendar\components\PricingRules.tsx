import type { CombinedPricingRule, DynamicPricing } from '../types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React from 'react'
import { createExampleCombinedRules } from '../utils/pricing'

interface PricingRulesProps {
  rules: CombinedPricingRule[]
  onChange: (rules: CombinedPricingRule[]) => void
}

/**
 * Component for managing combined pricing rules
 */
export const PricingRules: React.FC<PricingRulesProps> = ({ rules, onChange }) => {
  // Add a new rule or example rules if none exist
  const handleAddRule = () => {
    if (rules.length === 0) {
      // Add example rules for common scenarios
      onChange(createExampleCombinedRules())
    } else {
      // Add a single new rule
      const newRule: CombinedPricingRule = {
        id: `rule-${Date.now()}`,
        name: '<PERSON>uy tắc mới',
        days: [1, 2, 3, 4, 5], // Monday to Friday
        startTime: '17:00',
        endTime: '21:00',
        price: 400000,
      }
      onChange([...rules, newRule])
    }
  }

  // Delete a rule
  const handleDeleteRule = (index: number) => {
    const newRules = [...rules]
    newRules.splice(index, 1)
    onChange(newRules)
  }

  // Update a rule property
  const updateRule = (index: number, field: keyof CombinedPricingRule, value: any) => {
    const newRules = [...rules]
    // @ts-ignore
    newRules[index] = {
      ...newRules[index],
      [field]: value,
    }
    onChange(newRules)
  }

  // Toggle a day in the rule's days array
  const toggleDay = (index: number, day: number) => {
    const rule = rules[index]
    if (!rule) {
      return
    }

    const currentDays = [...rule.days]
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day]

    updateRule(index, 'days', newDays)
  }

  return (
    <div>
      <Label className="text-sm font-medium mb-2 block">Quy tắc giá theo ngày và giờ</Label>
      <div className="space-y-4">
        {rules.map((rule, index) => (
          <div key={rule.id} className="p-3 border rounded-md">
            <div className="flex justify-between items-center mb-2">
              <div className="flex-1">
                <Input
                  type="text"
                  value={rule.name}
                  onChange={e => updateRule(index, 'name', e.target.value)}
                  placeholder="Tên quy tắc (VD: Giờ cao điểm ngày thường)"
                  className="w-full"
                />
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-700 ml-2"
                onClick={() => handleDeleteRule(index)}
              >
                Xóa
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <Label className="text-xs">Từ giờ</Label>
                <Input
                  type="time"
                  value={rule.startTime}
                  onChange={e => updateRule(index, 'startTime', e.target.value)}
                  className="w-full"
                />
              </div>
              <div>
                <Label className="text-xs">Đến giờ</Label>
                <Input
                  type="time"
                  value={rule.endTime}
                  onChange={e => updateRule(index, 'endTime', e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            <div className="mb-3">
              <Label className="text-xs mb-1 block">Áp dụng cho các ngày</Label>
              <div className="flex flex-wrap gap-2">
                {[
                  { day: 0, label: 'CN' },
                  { day: 1, label: 'T2' },
                  { day: 2, label: 'T3' },
                  { day: 3, label: 'T4' },
                  { day: 4, label: 'T5' },
                  { day: 5, label: 'T6' },
                  { day: 6, label: 'T7' },
                ].map(({ day, label }) => (
                  <Button
                    key={day}
                    type="button"
                    variant={rule.days.includes(day) ? 'default' : 'outline'}
                    className="w-10 h-10 p-0"
                    onClick={() => toggleDay(index, day)}
                  >
                    {label}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-xs">Giá (VNĐ)</Label>
              <Input
                type="number"
                value={rule.price}
                onChange={e => updateRule(index, 'price', Number(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        ))}

        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleAddRule}
        >
          {rules.length === 0 ? 'Thêm quy tắc giá mẫu' : 'Thêm quy tắc giá'}
        </Button>
      </div>
    </div>
  )
}

/**
 * Helper component to manage dynamic pricing configuration
 */
export const DynamicPricingConfig: React.FC<{
  dynamicPricing: DynamicPricing | undefined
  onChange: (dynamicPricing: DynamicPricing) => void
}> = ({ dynamicPricing = { enabled: false, combinedRules: [] }, onChange }) => {
  // Update combined rules
  const handleRulesChange = (combinedRules: CombinedPricingRule[]) => {
    onChange({
      ...dynamicPricing,
      combinedRules,
    })
  }

  return (
    <div className="space-y-4">
      <PricingRules
        rules={dynamicPricing.combinedRules || []}
        onChange={handleRulesChange}
      />
    </div>
  )
}
