import type { AxiosInstance, AxiosProgressEvent, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { IResponse } from './api.model'
import axios from 'axios'

export class HttpClientLayer {
  private apiInstance: AxiosInstance

  constructor(config?: AxiosRequestConfig<any> | undefined) {
    this.apiInstance = axios.create(config)
  }

  handleSuccess<T>(response: AxiosResponse<any>) {
    return response.data as IResponse<T>
  }

  handleError(error: Error) {
    return Promise.reject(error)
  }

  config(config?: AxiosRequestConfig) {
    return config
  }

  async get<TResponse = any>(url: string, config?: AxiosRequestConfig) {
    try {
      const response: AxiosResponse<TResponse> = await this.apiInstance.get(url, config)
      return this.handleSuccess<TResponse>(response)
    } catch (error) {
      return this.handleError(error as Error)
    }
  }

  async post<TResponse = any, Payload = any>(url: string, data?: Payload, config?: AxiosRequestConfig<Payload>) {
    try {
      const response: AxiosResponse<TResponse, Payload> = await this.apiInstance.post(url, data, config)
      return this.handleSuccess<TResponse>(response)
    } catch (error) {
      return this.handleError(error as Error)
    }
  }

  async put<TResponse = any, Payload = any>(url: string, data?: Payload, config?: AxiosRequestConfig<Payload>) {
    try {
      const response: AxiosResponse<TResponse, Payload> = await this.apiInstance.put(url, data, config)
      return this.handleSuccess<TResponse>(response)
    } catch (error) {
      return this.handleError(error as Error)
    }
  }

  async delete<TResponse = any, Payload = any>(url: string, config?: AxiosRequestConfig<Payload>) {
    try {
      const response: AxiosResponse<TResponse, Payload> = await this.apiInstance.delete(url, config)
      return this.handleSuccess<TResponse>(response)
    } catch (error) {
      return this.handleError(error as Error)
    }
  }

  public async upload<T, P = unknown>(url: string, data: P, params = {}, onProgress?: (percentage: number) => void) {
    try {
      const onUploadProgress = (progressEvent: AxiosProgressEvent) => {
        const { loaded } = progressEvent
        const { total = 1 } = progressEvent
        const percent = Math.round((loaded / total) * 100)
        if (typeof onProgress === 'function') {
          onProgress(percent)
        }
      }
      const headers = { 'Content-Type': 'multipart/form-data' }
      const config: AxiosRequestConfig = {
        params,
        headers,
        onUploadProgress,
      }

      const r = await this.apiInstance.post(url, data, {
        ...config,
        timeout: 0,
      })
      return this.handleSuccess<T>(r)
    } catch (e) {
      throw this.handleError(e as Error)
    }
  }

  addInterceptor() {
    return this.apiInstance.interceptors
  }

  addRequestInterceptor() {
    return this.apiInstance.interceptors.request
  }

  addResponseInterceptor() {
    return this.apiInstance.interceptors.response
  }
}
