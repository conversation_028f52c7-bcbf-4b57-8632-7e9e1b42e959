'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import React from 'react'
import {
  Cell,
  Legend,
  Pie,
  Pie<PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'

type PieChartData = {
  name: string
  value: number
  color: string
}

type BookingPieChartProps = {
  data: PieChartData[]
  title: string
  description?: string
  showLegend?: boolean
}

export const BookingPieChart = ({
  data,
  title,
  description,
  showLegend = true,
}: BookingPieChartProps) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={value => [`${value} lượt đặt`, '']}
              />
              {showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
