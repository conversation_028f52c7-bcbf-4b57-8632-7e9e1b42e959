import type { MapBlock } from '../../types/blocks'
import React from 'react'

export interface MapBlockProps {
  iframe: string
}

export const MapBlockComponent: React.FC<MapBlockProps> = ({ iframe }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <h2 className="text-lg font-semibold mb-3"><PERSON><PERSON><PERSON></h2>
      <div className="w-full h-64 rounded overflow-hidden">
        <iframe
          // eslint-disable-next-line react-dom/no-unsafe-iframe-sandbox
          sandbox="allow-scripts allow-same-origin"
          src={iframe}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title="Event location map"
        />
      </div>
    </div>
  )
}

// Config component for the map block
export const MapBlockConfig: React.FC<{
  data: MapBlock['data']
  onChange: (data: MapBlock['data']) => void
}> = ({ data, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Map Configuration</h3>

      <div>
        <label className="block text-sm font-medium mb-1">Google Maps Embed URL</label>
        <input
          type="text"
          value={data.iframe}
          onChange={e => onChange({ ...data, iframe: e.target.value })}
          className="w-full p-2 border rounded-md"
          placeholder="https://www.google.com/maps/embed?pb=..."
        />
        <p className="text-xs text-gray-500 mt-1">
          Paste the embed URL from Google Maps (iframe src attribute)
        </p>
      </div>
    </div>
  )
}
