'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Calendar,
  Home,
  Settings,
  Users,
} from 'lucide-react'
import Link from 'next/link'
import React from 'react'

interface AdminLayoutProps {
  children: React.ReactNode
}

/**
 * Layout component for admin pages
 */
const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation */}
      <header className="border-b">
        <div className="container flex h-14 items-center">
          <div className="flex items-center gap-4">
            <Link href="/admin/dashboard">
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                <span className="hidden md:inline">Quản lý booking page</span>
              </Button>
            </Link>
            <span className="text-gray-400">/</span>
            <Link href="/admin/manage-booking-pages">
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span className="hidden md:inline">Booking Pages</span>
              </Button>
            </Link>
          </div>

          <div className="ml-auto flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Users className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 bg-gray-50">
        {children}
      </main>
    </div>
  )
}

export default AdminLayout
