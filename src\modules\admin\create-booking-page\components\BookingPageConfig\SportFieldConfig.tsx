'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import React, { useState } from 'react'
import { useTemplateConfig } from '../../hooks/useTemplateConfig'
import { BlockConfigRenderer } from '../BlockConfigRenderer'
import CollapsibleBlock from '../CollapsibleBlock'
import ThemeConfigPanel from '../ThemeConfigPanel'

/**
 * SportFieldConfig Component
 *
 * A configuration component for sport field templates
 */
const SportFieldConfig = () => {
  const {
    templateSelected,
    blocks,
    themeSettings,
    handleBlockChange,
    handleLayoutChange,
    handleColorChange,
    handleSave,
    addBlock,
    removeBlock,
    reorderBlocks,
  } = useTemplateConfig()

  const [activeTab, setActiveTab] = useState('blocks')

  // Sport field specific settings
  const [fieldType, setFieldType] = useState('football')

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          Cấu hình trang đặt sân
          {templateSelected.name}
        </CardTitle>
        <CardDescription>
          Điều chỉnh các khối nội dung và giao diện của trang đặt sân thể thao
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="blocks">Nội dung</TabsTrigger>
            <TabsTrigger value="theme">Giao diện</TabsTrigger>
            <TabsTrigger value="settings">Cài đặt sân</TabsTrigger>
          </TabsList>

          <TabsContent value="blocks">
            <BlockConfigRenderer
              blocks={blocks}
              onBlockChange={handleBlockChange}
              onReorderBlocks={reorderBlocks}
              onAddBlock={addBlock}
              onRemoveBlock={removeBlock}
            />
          </TabsContent>

          <TabsContent value="theme">
            <div className="space-y-6">
              <ThemeConfigPanel
                themeSettings={themeSettings}
                handleColorChange={handleColorChange}
                handleLayoutChange={handleLayoutChange}
                useCollapsible={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-6">
              <CollapsibleBlock title="Cài đặt sân thể thao" defaultOpen={true}>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3">Loại sân</h3>
                    <Select value={fieldType} onValueChange={setFieldType}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Chọn loại sân" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="football">Sân bóng đá</SelectItem>
                        <SelectItem value="basketball">Sân bóng rổ</SelectItem>
                        <SelectItem value="tennis">Sân tennis</SelectItem>
                        <SelectItem value="badminton">Sân cầu lông</SelectItem>
                        <SelectItem value="volleyball">Sân bóng chuyền</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium mb-3">Giờ mở cửa</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm mb-1">Giờ mở cửa</label>
                        <input
                          type="time"
                          defaultValue="06:00"
                          className="w-full p-2 border rounded"
                        />
                      </div>
                      <div>
                        <label className="block text-sm mb-1">Giờ đóng cửa</label>
                        <input
                          type="time"
                          defaultValue="22:00"
                          className="w-full p-2 border rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CollapsibleBlock>
            </div>
          </TabsContent>
        </Tabs>

        <Button type="button" className="w-full mt-6" onClick={handleSave}>
          Lưu cấu hình
        </Button>
      </CardContent>
    </Card>
  )
}

export default SportFieldConfig
