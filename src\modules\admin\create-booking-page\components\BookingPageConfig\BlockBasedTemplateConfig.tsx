'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import React, { useState } from 'react'
import { useTemplateConfig } from '../../hooks/useTemplateConfig'
import { BlockConfigRenderer } from '../BlockConfigRenderer'
import ThemeConfigPanel from '../ThemeConfigPanel'

/**
 * BlockBasedTemplateConfig Component
 *
 * A configuration component for block-based templates
 * This is the default template config used for EVENT_BOOKING templates
 */
const BlockBasedTemplateConfig = () => {
  const {
    templateSelected,
    blocks,
    themeSettings,
    handleBlockChange,
    handleLayoutChange,
    handleColorChange,
    handleSave,
    addBlock,
    removeBlock,
    reorderBlocks,
  } = useTemplateConfig()

  const [activeTab, setActiveTab] = useState('blocks')

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          Cấu hình trang
          {templateSelected.name}
        </CardTitle>
        <CardDescription>
          Điều chỉnh các khối nội dung và giao diện của trang đặt vé sự kiện
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="blocks">Nội dung</TabsTrigger>
            <TabsTrigger value="theme">Giao diện</TabsTrigger>
          </TabsList>

          <TabsContent value="blocks">
            <BlockConfigRenderer
              blocks={blocks}
              onBlockChange={handleBlockChange}
              onReorderBlocks={reorderBlocks}
              onAddBlock={addBlock}
              onRemoveBlock={removeBlock}
            />
          </TabsContent>

          <TabsContent value="theme">
            <div className="space-y-6">
              <ThemeConfigPanel
                themeSettings={themeSettings}
                handleColorChange={handleColorChange}
                handleLayoutChange={handleLayoutChange}
                useCollapsible={true}
              />
            </div>
          </TabsContent>
        </Tabs>

        <Button type="button" className="w-full mt-6" onClick={handleSave}>
          Lưu cấu hình
        </Button>
      </CardContent>
    </Card>
  )
}

export default BlockBasedTemplateConfig
