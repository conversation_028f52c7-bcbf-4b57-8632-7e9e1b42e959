'use client'

import type { ReactNode } from 'react'
import { motion } from 'framer-motion'

type AnimatedButtonProps = {
  children: ReactNode
  className?: string
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'outline'
}

export const AnimatedButton = ({
  children,
  className = '',
  onClick,
  variant = 'primary',
}: AnimatedButtonProps) => {
  const baseStyles = 'px-6 py-3 rounded-full font-medium transition-all duration-300'
  const variantStyles = {
    primary: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:shadow-lg',
    secondary: 'bg-white text-orange-500 hover:bg-orange-50',
    outline: 'border-2 border-orange-500 text-orange-500 hover:bg-orange-50',
  }

  return (
    <motion.button
      className={`${baseStyles} ${variantStyles[variant]} ${className}`}
      onClick={onClick}
      whileHover={{
        scale: 1.05,
        boxShadow: variant === 'primary' ? '0 10px 30px rgba(255,165,0,0.3)' : 'none',
      }}
      whileTap={{ scale: 0.95 }}
    >
      {children}
    </motion.button>
  )
}
