'use client'

import { SvgsAssets } from '@/assets/svgs'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'

import * as React from 'react'

const user = {
  name: '<PERSON><PERSON><PERSON><PERSON>ăn Agent',
  avatarUrl: 'https://i.pravatar.cc/100?img=3',
  plan: 'FREE',
}

export function TeamSwitcher() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          size="lg"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg text-sidebar-primary-foreground">
            <SvgsAssets.Logo />
          </div>
          <div className="grid flex-1 gap-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold flex-1">
              {user.name}
            </span>
            <span className="w-fit px-2 py-0.5 rounded-2xl font-semibold bg-gray-200 text-gray-600">
              {user.plan}
            </span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
