'use client'

import { SvgsAssets } from '@/assets/svgs'
import { Button } from '@/components/ui/button'
import { useRouter } from '@/libs/i18nNavigation'
import { getToken } from '@/services/auth'
import { appPaths } from '@/utils/app-routes'
import { gsap } from 'gsap'
import React, { useEffect, useRef, useState } from 'react'
import { FaCog, FaSignInAlt, FaUser, FaUserPlus } from 'react-icons/fa'

export const HeaderNavigation = () => {
  const router = useRouter()
  const headerRef = useRef<HTMLDivElement>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Check authentication status
  useEffect(() => {
    const token = getToken()
    setIsAuthenticated(!!token)
  }, [])

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate header on load
      gsap.from(headerRef.current, {
        y: -100,
        opacity: 0,
        duration: 1,
        ease: 'power3.out',
      })

      // Animate navigation items
      gsap.from('.nav-item', {
        y: -20,
        opacity: 0,
        duration: 0.6,
        stagger: 0.1,
        delay: 0.3,
        ease: 'power2.out',
      })
    }, headerRef)

    return () => ctx.revert()
  }, [])

  const handleLogin = () => {
    router.push(appPaths.auth.login())
  }

  const handleRegister = () => {
    router.push(appPaths.auth.register())
  }

  const handleDashboard = () => {
    router.push(appPaths.admin.dashboard())
  }

  const handleHome = () => {
    router.push(appPaths.public.home())
  }

  return (
    <header
      ref={headerRef}
      className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-100 shadow-sm"
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div
            role="button"
            tabIndex={0}
            className="nav-item flex items-center gap-3 cursor-pointer hover:opacity-80 transition-opacity"
            onClick={handleHome}
          >
            <SvgsAssets.Logo width={32} height={32} />
            <span className="text-xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
              PickSlot
            </span>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-2 md:gap-3">
            {isAuthenticated
              ? (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="nav-item flex items-center gap-1 md:gap-2 text-gray-600 hover:text-orange-500 px-2 md:px-3"
                      onClick={handleDashboard}
                    >
                      <FaCog className="w-4 h-4" />
                      <span className="hidden md:inline">Dashboard</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="nav-item flex items-center gap-1 md:gap-2 border-orange-500 text-orange-500 hover:bg-orange-50 px-2 md:px-3"
                      onClick={handleDashboard}
                    >
                      <FaUser className="w-4 h-4" />
                      <span className="hidden md:inline">Quản lý</span>
                    </Button>
                  </>
                )
              : (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="nav-item flex items-center gap-1 md:gap-2 text-gray-600 hover:text-orange-500 px-2 md:px-3"
                      onClick={handleLogin}
                    >
                      <FaSignInAlt className="w-4 h-4" />
                      <span className="hidden md:inline">Đăng nhập</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="nav-item flex items-center gap-1 md:gap-2 border-orange-500 text-orange-500 hover:bg-orange-50 px-2 md:px-3"
                      onClick={handleRegister}
                    >
                      <FaUserPlus className="w-4 h-4" />
                      <span className="hidden md:inline">Đăng ký</span>
                    </Button>

                    <Button
                      size="sm"
                      className="nav-item bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-3 md:px-4"
                      onClick={handleRegister}
                    >
                      <span className="text-sm">Bắt đầu</span>
                    </Button>
                  </>
                )}
          </div>
        </div>
      </div>
    </header>
  )
}
