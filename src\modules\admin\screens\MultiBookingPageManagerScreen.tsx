'use client'

import { PlusCircle } from 'lucide-react'
import React, { useState } from 'react'

// Mock data: danh s<PERSON>ch các booking page
const mockPages = [
  {
    id: 'page-1',
    name: '<PERSON><PERSON> bóng đá A',
    type: 'Đặt sân thể thao',
    bookingCount: 42,
    users: [
      { id: 'u1', name: '<PERSON><PERSON>', role: 'owner' },
      { id: 'u2', name: '<PERSON><PERSON><PERSON><PERSON>', role: 'manager' },
    ],
  },
  {
    id: 'page-2',
    name: 'Đặt xe VIP',
    type: 'Đặt xe',
    bookingCount: 17,
    users: [
      { id: 'u1', name: 'Admin', role: 'owner' },
    ],
  },
]

export default function MultiBookingPageManagerScreen() {
  const [pages, setPages] = useState(mockPages)
  const [selectedPageId, setSelectedPageId] = useState(pages[0]?.id || '')

  // Mock tạo mới page
  const handleCreatePage = () => {
    const newPage = {
      id: `page-${pages.length + 1}`,
      name: `Booking Page ${pages.length + 1}`,
      type: 'Đặt lịch đơn giản',
      bookingCount: 0,
      users: [{ id: 'u1', name: 'Admin', role: 'owner' }],
    }
    setPages([...pages, newPage])
    setSelectedPageId(newPage.id)
  }

  const selectedPage = pages.find(p => p.id === selectedPageId)

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-xl font-bold mb-6">Quản lý các trang booking</h1>
      <div className="flex gap-4 mb-6">
        {/* Sidebar danh sách page */}
        <div className="w-64 bg-white rounded-lg shadow p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between mb-2">
            <span className="font-semibold text-sm">Danh sách page</span>
            <button className="text-primary flex items-center gap-1 text-xs font-medium" onClick={handleCreatePage} type="button">
              <PlusCircle size={16} />
              {' '}
              Thêm mới
            </button>
          </div>
          <div className="flex flex-col gap-1">
            {pages.map(page => (
              <button
                key={page.id}
                className={`text-left px-3 py-2 rounded font-medium text-sm transition-all ${selectedPageId === page.id ? 'bg-primary/10 text-primary' : 'hover:bg-gray-100 text-gray-700'}`}
                onClick={() => setSelectedPageId(page.id)}
                type="button"
              >
                {page.name}
                <div className="text-xs text-gray-400">{page.type}</div>
              </button>
            ))}
          </div>
        </div>
        {/* Main content: thông tin page được chọn */}
        <div className="flex-1 bg-white rounded-lg shadow p-6">
          {selectedPage
            ? (
                <>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <div className="font-semibold text-lg">{selectedPage.name}</div>
                      <div className="text-sm text-gray-500">
                        Loại:
                        {selectedPage.type}
                      </div>
                    </div>
                    <button className="px-3 py-1.5 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 text-xs font-semibold">Cấu hình page</button>
                  </div>
                  <div className="mb-4">
                    <span className="font-medium">Lượng booking:</span>
                    {' '}
                    <span className="text-primary font-bold">{selectedPage.bookingCount}</span>
                  </div>
                  <div className="mb-4">
                    <span className="font-medium">Người quản lý:</span>
                    <ul className="list-disc ml-6 mt-1">
                      {selectedPage.users.map(u => (
                        <li key={u.id} className="text-sm">
                          {u.name}
                          {' '}
                          <span className="ml-2 text-xs text-gray-400">
                            (
                            {u.role === 'owner' ? 'Chủ sở hữu' : 'Quản lý'}
                            )
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <button className="px-3 py-1.5 rounded bg-blue-500 text-white font-semibold hover:bg-blue-600 text-xs">Thêm user quản lý</button>
                </>
              )
            : (
                <div className="text-gray-500">Chọn một trang booking để xem chi tiết</div>
              )}
        </div>
      </div>
    </div>
  )
}
