'use client'

import { Button } from '@/components/ui/button'
import { useRouter } from '@/libs/i18nNavigation'
import React, { useRef } from 'react'
import { FaHome } from 'react-icons/fa'

export const TermsHeader = () => {
  const router = useRouter()
  const headerRef = useRef<HTMLDivElement>(null)
  const logoRef = useRef<HTMLDivElement>(null)
  const navRef = useRef<HTMLDivElement>(null)

  // useEffect(() => {
  //   const ctx = gsap.context(() => {
  //     // Animate header elements
  //     gsap.from(logoRef.current, {
  //       y: -20,
  //       opacity: 0,
  //       duration: 0.5,
  //     })

  //     gsap.from(navRef.current?.children || [], {
  //       y: -20,
  //       opacity: 0,
  //       duration: 0.5,
  //       stagger: 0.1,
  //       delay: 0.2,
  //     })
  //   }, headerRef)

  //   return () => ctx.revert()
  // }, [])

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  return (
    <header
      ref={headerRef}
      className="bg-white shadow-sm py-4 sticky top-0 z-50"
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div
            role="button"
            tabIndex={0}
            ref={logoRef}
            className="flex items-center cursor-pointer"
            onClick={() => handleNavigation('/')}
          >
            <div className="text-orange-500 text-2xl font-bold">PickSlot</div>
          </div>

          {/* Navigation */}
          <div ref={navRef} className="flex items-center space-x-2 md:space-x-4">
            <Button
              variant="ghost"
              className="flex items-center gap-1 md:gap-2 text-gray-600 hover:text-orange-500 px-2 md:px-4"
              onClick={() => handleNavigation('/')}
            >
              <FaHome />
              <span className="hidden md:inline">Trang chủ</span>
            </Button>

            <Button
              variant="outline"
              className="flex items-center text-xs md:text-sm border-orange-500 text-orange-500 hover:bg-orange-50 px-2 md:px-4"
              onClick={() => handleNavigation('/auth/signup')}
            >
              <span>Đăng ký</span>
            </Button>

            <Button
              className="flex items-center text-xs md:text-sm bg-orange-500 text-white hover:bg-orange-600 px-2 md:px-4"
              onClick={() => handleNavigation('/auth/signin')}
            >
              <span>Đăng nhập</span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}
