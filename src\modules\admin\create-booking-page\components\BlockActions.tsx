'use client'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { ArrowDownIcon, ArrowUpIcon, Trash2Icon } from 'lucide-react'
import React from 'react'

interface BlockActionsProps {
  index: number
  totalBlocks: number
  onMoveUp?: (index: number) => void
  onMoveDown?: (index: number) => void
  onRemove?: (index: number) => void
}

/**
 * BlockActions Component
 *
 * Renders a set of action buttons for a block (move up, move down, remove)
 */
const BlockActions: React.FC<BlockActionsProps> = ({
  index,
  totalBlocks,
  onMoveUp,
  onMoveDown,
  onRemove,
}) => {
  return (
    <div className="flex items-center space-x-1">
      <TooltipProvider>
        {onMoveUp && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onMoveUp(index)}
                disabled={index === 0}
              >
                <ArrowUpIcon className="h-4 w-4" />
                <span className="sr-only">Move Up</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Move Up</p>
            </TooltipContent>
          </Tooltip>
        )}

        {onMoveDown && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onMoveDown(index)}
                disabled={index === totalBlocks - 1}
              >
                <ArrowDownIcon className="h-4 w-4" />
                <span className="sr-only">Move Down</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Move Down</p>
            </TooltipContent>
          </Tooltip>
        )}

        {onRemove && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={() => onRemove(index)}
              >
                <Trash2Icon className="h-4 w-4" />
                <span className="sr-only">Remove Block</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Remove Block</p>
            </TooltipContent>
          </Tooltip>
        )}
      </TooltipProvider>
    </div>
  )
}

// Add display name for debugging
BlockActions.displayName = 'BlockActions'

export default BlockActions
