---
description: 
globs: 
alwaysApply: true
---

# Your rule content

Khi viết tính năng đi theo rule như sau:
- viết path route thì src/app/[locale](marketing)/{prefix nếu có}/{tên module}/{tên module}.page.tsx
- viết path route thì src/app/[locale](auth)/{prefix nếu có}/{tên module}/{tên module}.page.tsx
- viết module ở src/modules/{tên module}
- trong module có cấu trúc như sau
+ api
+ constants
+ components
+ hook
+ screens
+ services
+ types
+ {tên module}.api.ts
+ {tên module}.actions.ts

Tối ưu code theo các nguyên tắc sau
- Tách dữ liệu tĩnh:
- Tạo components tái sử dụng
+ sử dụng các component có sẳn, như src/component/ui
+ sử dụng react-use tránh viết nhiều code ko cần thiết
- <PERSON><PERSON><PERSON> trú<PERSON> thư mục rõ ràng
- Tối ưu hiệu suất:
+ Sử dụng memo cho các components
+ Tách logic animation thành components riêng
+ Giảm số lần render không cần thiết
- Cải thiện UX:
+ Thêm loading states
+ Thêm error handling
+ Thêm hover và tap effects
+ Thêm transitions mượt mà
- TypeScript:
+ Định nghĩa rõ ràng các types
+ Tăng tính type-safe
+ Dễ dàng refactor
- Responsive Design:
+ Sử dụng Tailwind CSS
+ Grid system linh hoạt
+ Mobile-first approach
- Clean Code:
+ Tên biến và hàm rõ ràng
+ Tách logic thành các functions nhỏ
- Comment giải thích khi cần thiết


- Nếu trong component mà sử dụng các loại hook hãy define `use client` trên đầu file
+ `use client` trên đầu file
+ nếu sử dụng các loại hook thì import từ `react`
++ `use state`
++ `use effect`
++ `use callback`
++ `use memo`
++ `use context`
++ `use ref`
++ `use transition`



