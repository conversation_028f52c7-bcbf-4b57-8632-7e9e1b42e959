'use client'

/**
 * @deprecated This component is deprecated and will be removed in a future version.
 * Please use BlockBasedTemplateConfig instead.
 */

import type { EventConfigFormValues } from './schema/eventConfigSchema'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Form } from '@/components/ui/form'
import React from 'react'

// Import custom components and config
import { FieldRenderer } from './components/FieldRenderer'
import { eventFieldsConfig } from './config/eventFieldsConfig'
import { useEventConfigForm } from './hooks/useEventConfigForm'

type Props = {
  defaultValues?: Partial<EventConfigFormValues>
  onSubmit?: (values: EventConfigFormValues) => void
}

/**
 * @deprecated This component is deprecated and will be removed in a future version.
 * Please use BlockBasedTemplateConfig instead.
 *
 * EventConfig Component
 *
 * A form component for configuring event booking pages.
 * Uses a configuration array to dynamically render form fields.
 */
const EventConfig = ({ defaultValues, onSubmit }: Props) => {
  const { form, handleSubmit } = useEventConfigForm({ defaultValues, onSubmit })

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Cấu hình trang đặt vé sự kiện</CardTitle>
        <CardDescription>
          Điều chỉnh các thông tin hiển thị trên trang đặt vé sự kiện của bạn
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Render fields dynamically from config */}
              {eventFieldsConfig.map(fieldConfig => (
                <React.Fragment key={fieldConfig.name.toString()}>
                  <FieldRenderer field={fieldConfig} form={form} />
                </React.Fragment>
              ))}
            </div>

            <Button type="submit" className="w-full">
              Lưu cấu hình
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default EventConfig
