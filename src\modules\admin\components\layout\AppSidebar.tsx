'use client'

import { <PERSON>bar, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, SidebarHeader } from '@/components/ui'
import React from 'react'
import { NavProjects } from './NavMain'
import { TeamSwitcher } from './TeamSwitcher'

type Props = React.ComponentProps<typeof Sidebar>

const AppSidebar = ({ ...props }: Props) => {
  return (
    <Sidebar
      collapsible="icon"
      {...props}
    >
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavProjects />
      </SidebarContent>
      <SidebarFooter />
    </Sidebar>
  )
}

export default AppSidebar
