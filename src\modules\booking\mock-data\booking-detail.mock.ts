import type { BookingDetail } from '@/modules/booking/booking.apis'

export const MOCK_BOOKING_DETAIL: BookingDetail = {
  id: 'booking-123456',
  bookingPageId: 'page-abc123',
  bookingPageName: 'Sân Tennis Hoàng Gia',
  slug: 'san-tennis-hoang-gia',
  customerName: '<PERSON>uy<PERSON><PERSON>',
  customerEmail: 'nguy<PERSON><PERSON><EMAIL>',
  customerPhone: '0901234567',
  bookingDate: '2024-01-15',
  bookingSlots: [
    {
      date: '2024-01-15',
      field: 'Sân 1',
      time: '18:00 - 20:00',
      price: 300000,
    },
    {
      date: '2024-01-15',
      field: 'Sân 2',
      time: '20:00 - 22:00',
      price: 350000,
    },
  ],
  paymentMethod: 'COD',
  quantity: 2,
  notes: '<PERSON>h<PERSON>ch hàng VIP, cần chuẩn bị nước uống miễn phí',
  status: 'confirmed',
  totalPrice: 650000,
  createdAt: '2024-01-10T10:30:00Z',
  updatedAt: '2024-01-12T14:20:00Z',
}

export const MOCK_BOOKING_DETAIL_PENDING: BookingDetail = {
  id: 'booking-789012',
  bookingPageId: 'page-def456',
  bookingPageName: 'Sân Bóng Đá Mini ABC',
  slug: 'san-bong-da-mini-abc',
  customerName: 'Trần Thị Bình',
  customerEmail: '<EMAIL>',
  customerPhone: '**********',
  bookingDate: '2024-01-20',
  bookingSlots: [
    {
      date: '2024-01-20',
      field: 'Sân A',
      time: '08:00 - 10:00',
      price: 200000,
    },
  ],
  paymentMethod: 'Bank Transfer',
  quantity: 1,
  notes: '',
  status: 'pending',
  totalPrice: 200000,
  createdAt: '2024-01-18T09:15:00Z',
}

export const MOCK_BOOKING_DETAIL_CANCELLED: BookingDetail = {
  id: 'booking-345678',
  bookingPageId: 'page-ghi789',
  bookingPageName: 'Phòng Gym Premium',
  slug: 'phong-gym-premium',
  customerName: 'Lê Văn Cường',
  customerEmail: '<EMAIL>',
  customerPhone: '**********',
  bookingDate: '2024-01-25',
  bookingSlots: [
    {
      date: '2024-01-25',
      field: 'Phòng VIP',
      time: '14:00 - 16:00',
      price: 500000,
    },
  ],
  paymentMethod: 'Credit Card',
  quantity: 1,
  notes: 'Hủy do lý do cá nhân',
  status: 'cancelled',
  totalPrice: 500000,
  createdAt: '2024-01-22T16:45:00Z',
  updatedAt: '2024-01-24T11:30:00Z',
}
