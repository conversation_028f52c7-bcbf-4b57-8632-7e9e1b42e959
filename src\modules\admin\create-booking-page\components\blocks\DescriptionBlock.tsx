import type { DescriptionBlock } from '../../types/blocks'
import React from 'react'

export interface DescriptionBlockProps {
  content: string
}

export const DescriptionBlockComponent: React.FC<DescriptionBlockProps> = ({ content }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <h2 className="text-lg font-semibold mb-3">G<PERSON><PERSON>i thiệu sự kiện</h2>
      <div className="prose max-w-none">
        {content.split('\n').map((paragraph, index) => (
          <p key={index} className="mb-2">{paragraph}</p>
        ))}
      </div>
    </div>
  )
}

// Config component for the description block
export const DescriptionBlockConfig: React.FC<{
  data: DescriptionBlock['data']
  onChange: (data: DescriptionBlock['data']) => void
}> = ({ data, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Event Description</h3>

      <div>
        <label className="block text-sm font-medium mb-1">Content</label>
        <textarea
          value={data.content}
          onChange={e => onChange({ ...data, content: e.target.value })}
          className="w-full p-2 border rounded-md min-h-[150px]"
          placeholder="Describe your event here..."
        />
        <p className="text-xs text-gray-500 mt-1">
          Use line breaks to separate paragraphs.
        </p>
      </div>
    </div>
  )
}
