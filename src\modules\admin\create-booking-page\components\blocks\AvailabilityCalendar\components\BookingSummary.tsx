import type { BookingSummaryProps } from '../types'
import { format } from 'date-fns'
import React, { useMemo } from 'react'
import { useSelectedBookings } from '../store/availabilityCalendarStore'

/**
 * Booking summary component for displaying selected booking details
 */
export const BookingSummary: React.FC<BookingSummaryProps> = (props) => {
  const { displayFields } = props

  // Get bookings from the store
  const selectedBookings = useSelectedBookings()

  // Group bookings by field
  const bookingsByField = useMemo(() => {
    const grouped = new Map()

    selectedBookings.forEach((slot) => {
      if (!grouped.has(slot.field)) {
        grouped.set(slot.field, [])
      }
      grouped.get(slot.field).push(slot)
    })

    return grouped
  }, [selectedBookings])

  return (
    <div className="mb-6 p-4 border rounded-md bg-blue-50">
      <h3 className="font-medium mb-2">Thông tin đặt sân</h3>
      <div className="space-y-4">
        {/* Date */}
        <div className="flex justify-between">
          <span className="text-gray-600">Ngày:</span>
          <span className="font-medium">{selectedBookings.length > 0 ? format(selectedBookings[0]!.date, 'dd/MM/yyyy') : ''}</span>
        </div>

        {/* Bookings by field */}
        {Array.from(bookingsByField.entries()).map(([fieldId, slots]) => {
          const field = displayFields.find(f => f.id === fieldId)
          return (
            <div key={fieldId} className="border-t pt-2">
              <div className="flex justify-between font-medium">
                <span>{field?.name}</span>
                <span>
                  {slots.length}
                  {' '}
                  khung giờ
                </span>
              </div>
              <div className="mt-1 text-sm">
                <span className="text-gray-600">Thời gian: </span>
                <span>
                  {slots.map((slot: any) => slot.time).join(', ')}
                </span>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
