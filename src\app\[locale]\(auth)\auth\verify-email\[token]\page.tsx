import EmailVerificationScreen from '@/modules/auth/screens/EmailVerificationScreen'
import { getTranslations, setRequestLocale } from 'next-intl/server'

type EmailVerificationPageProps = {
  params: Promise<{ locale: string, token: string }>
}

export async function generateMetadata(props: EmailVerificationPageProps) {
  const { locale } = await props.params
  const t = await getTranslations({
    locale,
    namespace: 'EmailVerification',
  })

  return {
    title: t('meta_title', { fallback: 'Verify Email - Booking Easy' }),
    description: t('meta_description', { fallback: 'Verify your email address to activate your account' }),
  }
}

export default async function EmailVerificationPage(props: EmailVerificationPageProps) {
  const { locale, token } = await props.params
  setRequestLocale(locale)

  return (
    <EmailVerificationScreen token={token} />
  )
}
