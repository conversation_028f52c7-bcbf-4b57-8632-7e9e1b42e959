import HomeScreen from '@/modules/home/<USER>/HomeScreen'
import { getTranslations } from 'next-intl/server'

type IIndexProps = {
  params: Promise<{ locale: string }>
}

export async function generateMetadata(props: IIndexProps) {
  const { locale } = await props.params
  const t = await getTranslations({
    locale,
    namespace: 'Index',
  })

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  }
}

export default async function Index() {
  return (<HomeScreen />)
};
