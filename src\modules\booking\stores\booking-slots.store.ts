import type { BookedSlot } from '@/modules/booking/booking.apis'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import { enableMapSet } from 'immer'
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

// Enable Map and Set support in Immer
enableMapSet()

// Helper function to format date to YYYY-MM-DD using local timezone
const formatDateToString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return date
  }

  // Use local timezone instead of UTC to avoid date shifting
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

interface BookingSlotsState {
  // State
  bookedSlots: BookedSlot[]
  selectedDate: string | null
  bookingPageId: string | null
  isLoading: boolean
  error: string | null
  // Optimized lookup maps for O(1) slot status checking
  slotLookupMap: Map<string, BookedSlot>

  // Actions
  setBookingPageId: (id: string) => Promise<void>
  setSelectedDate: (date: string) => void
  setSelectedDateAndLoadSlots: (date: string) => Promise<void>
  setSelectedDateFromDateObject: (date: Date) => Promise<void>
  loadBookedSlots: (bookingPageId: string, date: string) => Promise<void>
  clearBookedSlots: () => void
  isSlotBooked: (field: string, time: string) => boolean
  getSlotStatus: (field: string, time: string) => 'available' | 'pending' | 'confirmed'
  // New action to reload current slots
  reloadCurrentSlots: () => Promise<void>
  // Internal helper to rebuild lookup maps
  rebuildLookupMaps: () => void
}

// Helper function to create slot lookup key
const createSlotKey = (field: string, time: string): string => {
  return `${field}:${time}`
}

export const useBookingSlotsStore = create<BookingSlotsState>()(
  immer((set, get) => ({
    // Initial state
    bookedSlots: [],
    selectedDate: null,
    bookingPageId: null,
    isLoading: false,
    error: null,
    slotLookupMap: new Map(),

    // Set booking page ID and load slots if date is already selected
    setBookingPageId: async (id: string) => {
      set((state) => {
        state.bookingPageId = id
      })
    },

    // Set selected date
    setSelectedDate: (date: string) => {
      set((state) => {
        state.selectedDate = date
      })
    },

    // Set selected date and automatically load booked slots
    setSelectedDateAndLoadSlots: async (date: string) => {
      const { bookingPageId, loadBookedSlots } = get()

      // Set the selected date first
      set((state) => {
        state.selectedDate = date
      })

      // If we have a booking page ID, load the booked slots for this date
      if (bookingPageId) {
        await loadBookedSlots(bookingPageId, date)
      }
    },

    // Set selected date from Date object and automatically load booked slots
    setSelectedDateFromDateObject: async (date: Date) => {
      const dateString = formatDateToString(date)
      const { bookingPageId, loadBookedSlots } = get()

      // Set the selected date first
      set((state) => {
        state.selectedDate = dateString
      })

      // If we have a booking page ID, load the booked slots for this date
      if (bookingPageId) {
        await loadBookedSlots(bookingPageId, dateString)
      }
    },

    // Load booked slots for a specific date
    loadBookedSlots: async (bookingPageId: string, date: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await bookingAPIs.getBookedSlots({
          bookingPageId,
          date,
        })

        if (response.status?.success) {
          set((state) => {
            state.bookedSlots = response.data?.bookedSlots || []
            state.selectedDate = date
            state.bookingPageId = bookingPageId
            state.isLoading = false
          })
          // Rebuild lookup maps after updating booked slots
          get().rebuildLookupMaps()
        } else {
          throw new Error(response.status?.message || 'Failed to load booked slots')
        }
      } catch (error) {
        console.error('Error loading booked slots:', error)
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Unknown error'
          state.isLoading = false
          state.bookedSlots = []
        })
        // Rebuild lookup maps after clearing booked slots
        get().rebuildLookupMaps()
      }
    },

    // Clear booked slots
    clearBookedSlots: () => {
      set((state) => {
        state.bookedSlots = []
        state.selectedDate = null
        state.error = null
        state.slotLookupMap.clear()
      })
    },

    // Check if a specific slot is booked (optimized with O(1) lookup)
    isSlotBooked: (field: string, time: string) => {
      const { slotLookupMap } = get()
      const key = createSlotKey(field, time)
      const slot = slotLookupMap.get(key)

      return slot ? slot.status !== 'cancelled' : false
    },

    // Get slot status (optimized with O(1) lookup)
    getSlotStatus: (field: string, time: string) => {
      const { slotLookupMap } = get()
      const key = createSlotKey(field, time)
      const slot = slotLookupMap.get(key)

      if (!slot || slot.status === 'cancelled') {
        return 'available'
      }

      return slot.status
    },

    // Reload current slots - useful after booking submission
    reloadCurrentSlots: async () => {
      const { bookingPageId, selectedDate, loadBookedSlots } = get()

      if (bookingPageId && selectedDate) {
        await loadBookedSlots(bookingPageId, selectedDate)
      }
    },

    // Rebuild lookup maps for optimized slot checking
    rebuildLookupMaps: () => {
      set((state) => {
        const newSlotLookupMap = new Map<string, BookedSlot>()

        // Build lookup map for O(1) slot status checking
        state.bookedSlots.forEach((slot) => {
          const key = createSlotKey(slot.field, slot.time)
          newSlotLookupMap.set(key, slot)
        })

        state.slotLookupMap = newSlotLookupMap
      })
    },
  })),
)
