import { FaFutbol, FaTableTennis, FaUtensils } from 'react-icons/fa'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/gi'

export type Category = {
  id: string
  name: string
  icon: React.ReactNode
  description: string
}

export const CATEGORIES: Category[] = [
  {
    id: 'football',
    name: 'Sân bóng đá',
    icon: <FaFutbol className="text-2xl" />,
    description: 'Đặt sân 5-7-11 người',
  },
  {
    id: 'badminton',
    name: 'Cầ<PERSON> lông',
    icon: <GiShuttlecock className="text-2xl" />,
    description: 'Sân trong nhà, ngoài trời',
  },
  {
    id: 'tabletennis',
    name: '<PERSON><PERSON><PERSON> bàn',
    icon: <FaTableTennis className="text-2xl" />,
    description: '<PERSON>àn đơn, đôi',
  },
  {
    id: 'restaurant',
    name: '<PERSON><PERSON><PERSON> hàng',
    icon: <FaUtensils className="text-2xl" />,
    description: 'Đặt bàn, tiệc',
  },
]
