'use client'

import type { BookingAvailability } from '../components/BookingAvailabilitySetting'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import React, { useState } from 'react'
import { toast } from 'sonner'
import { BookingAvailabilitySetting } from '../components/BookingAvailabilitySetting'
import { MOCK_BOOKING_PAGES } from '../constants/mock-pages'

const DEFAULT_AVAILABILITY: BookingAvailability = { dates: [], slots: [] }

const BookingAvailabilitySettingScreen: React.FC = () => {
  const [selectedPageId, setSelectedPageId] = useState(MOCK_BOOKING_PAGES[0]?.id || '')
  // L<PERSON>u lịch mở cho từng page
  const [pageAvailability, setPageAvailability] = useState<Record<string, BookingAvailability>>({
    [MOCK_BOOKING_PAGES[0]?.id || '']: DEFAULT_AVAILABILITY,
    [MOCK_BOOKING_PAGES[1]?.id || '']: DEFAULT_AVAILABILITY,
    [MOCK_BOOKING_PAGES[2]?.id || '']: DEFAULT_AVAILABILITY,
  })

  const currentAvailability = pageAvailability[selectedPageId] || DEFAULT_AVAILABILITY

  const handleSubmit = (data: BookingAvailability) => {
    setPageAvailability(prev => ({ ...prev, [selectedPageId]: data }))
    toast(`Đã lưu lịch mở cho page: ${MOCK_BOOKING_PAGES.find(p => p.id === selectedPageId)?.name || ''}`)
  }

  return (
    <div className="max-w-2xl mx-auto py-8 px-2 sm:px-4 md:px-8">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">Cấu hình lịch mở từng trang booking</h1>
        <div className="mb-6 flex items-center gap-4">
          <Select value={selectedPageId} onValueChange={setSelectedPageId}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Chọn trang booking" />
            </SelectTrigger>
            <SelectContent>
              {MOCK_BOOKING_PAGES.map(page => (
                <SelectItem key={page.id} value={page.id} className="flex gap-2 items-center">
                  <div className="flex gap-2 items-center">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={page.avatarUrl} />
                      <AvatarFallback>{page.name[0]}</AvatarFallback>
                    </Avatar>
                    <span>{page.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <BookingAvailabilitySetting initialAvailability={currentAvailability} onSubmit={handleSubmit} />
      </Card>
    </div>
  )
}

export default BookingAvailabilitySettingScreen
