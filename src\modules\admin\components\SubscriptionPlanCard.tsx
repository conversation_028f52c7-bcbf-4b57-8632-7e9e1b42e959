import type { SubscriptionPlan } from '../constants/subscription-plans'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Check } from 'lucide-react'
import React from 'react'

type SubscriptionPlanCardProps = {
  plan: SubscriptionPlan
  current: boolean
  onSelect: () => void
}

export const SubscriptionPlanCard: React.FC<SubscriptionPlanCardProps> = ({ plan, current, onSelect }) => {
  return (
    <Card className={`p-6 flex flex-col gap-4 border-2 ${current ? 'border-primary bg-blue-50' : 'border-gray-200'}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">{plan.name}</h2>
        {current && <span className="text-xs px-2 py-1 bg-primary text-white rounded">Đang dùng</span>}
      </div>
      <div className="text-2xl font-semibold text-primary">{plan.priceLabel}</div>
      <div className="text-gray-500 mb-2">{plan.description}</div>
      <ul className="mb-4 space-y-1">
        {plan.features.map(f => (
          <li key={f} className="flex items-center gap-2 text-sm">
            <Check size={16} className="text-green-600" />
            {' '}
            {f}
          </li>
        ))}
      </ul>
      <Button
        type="button"
        variant={current ? 'secondary' : 'default'}
        disabled={current}
        className="w-full"
        onClick={onSelect}
      >
        {current ? 'Gói hiện tại' : 'Nâng cấp'}
      </Button>
    </Card>
  )
}

export default SubscriptionPlanCard
