import type { AvailabilityGridProps } from '../types'
import { cn } from '@/libs/utils'
import { useBookingSlotsStore } from '@/modules/booking/stores/booking-slots.store'
import { CheckCircle, Clock, MapPin, User } from 'lucide-react'
import React, { useCallback, useMemo } from 'react'
import { useIsSlotSelected, useToggleBookingSlot } from '../store/availabilityCalendarStore'

// Pre-defined styling configurations for better performance
const SLOT_STYLING_CONFIG = {
  selected: {
    containerClass: 'bg-blue-100 border-blue-300 ring-2 ring-blue-200 hover:bg-blue-200',
    textClass: 'text-blue-700',
    statusText: 'Đã chọn',
    cursor: 'cursor-pointer',
  },
  available: {
    containerClass: 'bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300',
    textClass: 'text-green-700',
    statusText: 'Còn trống',
    cursor: 'cursor-pointer',
  },
  pending: {
    containerClass: 'bg-yellow-50 border-yellow-200',
    textClass: 'text-yellow-700',
    statusText: 'Chờ xác nhận',
    cursor: 'cursor-not-allowed',
  },
  confirmed: {
    containerClass: 'bg-red-50 border-red-200',
    textClass: 'text-red-700',
    statusText: 'Đã đặt',
    cursor: 'cursor-not-allowed',
  },
  default: {
    containerClass: 'bg-gray-50 border-gray-200',
    textClass: 'text-gray-500',
    statusText: 'Không khả dụng',
    cursor: 'cursor-not-allowed',
  },
} as const

// Pre-defined icons for better performance
const SLOT_ICONS = {
  selected: <User className="h-4 w-4" />,
  available: <CheckCircle className="h-4 w-4" />,
  pending: <Clock className="h-4 w-4" />,
  confirmed: <CheckCircle className="h-4 w-4 fill-current" />,
  default: <MapPin className="h-4 w-4" />,
} as const

// Optimized slot cell component
interface SlotCellProps {
  fieldId: string
  fieldName: string
  time: string
  slotStatus: 'available' | 'pending' | 'confirmed'
  isSelected: boolean
  onToggle: (fieldId: string, time: string) => void
}

const SlotCellBase: React.FC<SlotCellProps> = ({
  fieldId,
  fieldName,
  time,
  slotStatus,
  isSelected,
  onToggle,
}) => {
  // Get styling configuration based on state
  const stylingKey = isSelected ? 'selected' : (slotStatus in SLOT_STYLING_CONFIG ? slotStatus : 'default')
  const styling = SLOT_STYLING_CONFIG[stylingKey]
  const icon = SLOT_ICONS[stylingKey]

  const handleClick = useCallback(() => {
    if (slotStatus === 'available') {
      onToggle(fieldId, time)
    }
  }, [fieldId, time, slotStatus, onToggle])

  return (
    <div
      role="button"
      tabIndex={0}
      className={cn(
        'p-3 h-24 border-r flex items-center justify-center transition-all duration-200 border-2',
        styling.containerClass,
        styling.cursor,
      )}
      onClick={handleClick}
    >
      <div className="flex flex-col items-center justify-center w-full space-y-1">
        {/* Time slot indicator */}
        <div className={cn(
          'text-xs font-semibold rounded-full px-2 py-1',
          isSelected ? 'bg-blue-200 text-blue-800' : 'bg-white/80 text-gray-700',
        )}
        >
          {time}
        </div>

        {/* Status indicator with icon and text */}
        <div className={cn(
          'flex items-center gap-1 text-xs font-medium',
          styling.textClass,
        )}
        >
          {icon}
          <span>{styling.statusText}</span>
        </div>

        {/* Field location indicator */}
        <div className="flex items-center gap-1 text-xs text-gray-600">
          <MapPin className="h-3 w-3" />
          <span className="truncate max-w-[60px]">{fieldName}</span>
        </div>
      </div>
    </div>
  )
}

const SlotCell = React.memo(SlotCellBase)

/**
 * Availability grid component for displaying time slots and fields
 */
export const AvailabilityGridBase: React.FC<AvailabilityGridProps> = ({
  timeSlots,
  displayFields,
  selectedDate,
}) => {
  // Get helpers from the stores
  const isSlotSelected = useIsSlotSelected()
  const toggleBookingSlot = useToggleBookingSlot()
  const { getSlotStatus } = useBookingSlotsStore()

  // Memoized toggle handler
  const handleToggleBookingSlot = useCallback((fieldId: string, time: string) => {
    toggleBookingSlot(fieldId, time, selectedDate)
  }, [toggleBookingSlot, selectedDate])

  // Memoized grid template columns
  const gridTemplateColumns = useMemo(() =>
    `repeat(${displayFields.length}, 1fr)`, [displayFields.length])

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
      <div className="min-w-[600px]">
        {/* Header with field names */}
        <div className="grid border-b" style={{ gridTemplateColumns }}>
          {displayFields.map(field => (
            <div
              key={field.id}
              className="p-2 text-center font-medium border-r last:border-r-0 bg-gray-50"
            >
              <div>{field.name}</div>
            </div>
          ))}
        </div>

        {/* Time slots */}
        <div>
          {timeSlots.map(slot => (
            <div
              key={`slot-${slot.time}`}
              className="grid border-b"
              style={{ gridTemplateColumns }}
            >
              {/* Availability cells for each field */}
              {displayFields.map((field) => {
                const slotStatus = getSlotStatus(field.id, slot.time)
                const isSelected = isSlotSelected(field.id, slot.time)

                return (
                  <SlotCell
                    key={`${field.id}-${slot.time}`}
                    fieldId={field.id}
                    fieldName={field.name}
                    time={slot.time}
                    slotStatus={slotStatus}
                    isSelected={isSelected}
                    onToggle={handleToggleBookingSlot}
                  />
                )
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export const AvailabilityGrid = React.memo(AvailabilityGridBase)
