=== TICKET LIST: DASHBOARD UI (AGENT) - NEXT.JS ===
>> Ưu tiên hoàn thiện UI trước, dùng mock data nếu backend chưa xong
>> <PERSON><PERSON> khi xong UI mới kết nối với API & socket
>> Mỗi task có thể check hoàn thành bằng cách đánh dấu [x]

[x] [DASH-UI-001] Tạo layout cho dashboard agent
- Thiết kế layout tổng thể của dashboard: sidebar, header, main content
- Mock user info (agent name, avatar...)
- Ưu tiên: <PERSON>

[x] [DASH-UI-002] Trang "Tạo mới booking page" cho agent
- Cho phép chọn UI template - Có 5 loại template:
  + Template 1: Đặt sân thể thao - Giờ liên tục (chọn ngày, sân, giờ bắt đầu và kết thúc, số người)
  + Template 2: Đặt sân thể thao - <PERSON> khun<PERSON> gi<PERSON> (ch<PERSON><PERSON> ng<PERSON><PERSON>, sân, slot thời gian 15'/30', số người)
  + Template 3: Đặt lịch đơn giản (chỉ chọn ngày và booking)
  + Template 4: Hỗ trợ sự kiện (đặt chỗ cho sự kiện, vị trí ngồi, số người tham gia)
  + Template 5: Đặt xe - Vị trí xe (đặt xe với vị trí, thời gian đón, loại xe, điểm đến)
- Cho phép nhập tên page, mô tả, hình đại diện
- Xem trước template đã chọn + thông tin đã nhập
- Ưu tiên: Cao

[x] [DASH-UI-003] Trang "Quản lý booking page" đã tạo
- Hiển thị danh sách các trang agent đã tạo
- Gồm: Tên page, trạng thái, ngày tạo, nút “Cập nhật”
- Ưu tiên: Trung bình

[x] [DASH-UI-004] Giao diện bảng booking theo ngày/tuần/tháng
- Table booking với mock data, chọn được filter thời gian
- Hiển thị các cột: Tên người book, thời gian, trạng thái, nút hành động
- Ưu tiên: Cao

[x] [DASH-UI-005] Hiển thị thống kê số lượng booking theo thời gian
- Giao diện biểu đồ đơn giản: Bar / Line Chart
- Mock dữ liệu tổng số booking theo tuần/tháng
- Ưu tiên: Trung bình

[x] [DASH-UI-006] Giao diện Accept / Reject booking
- Tạo nút hành động trong bảng booking để xác nhận / từ chối
- Hiển thị popup xác nhận khi nhấn nút
- Ưu tiên: Cao

[x] [DASH-UI-007] Giao diện tạo booking thủ công (Agent booking hộ)
- Form tạo booking: Tên người nhận, ngày giờ, ghi chú
- Validate dữ liệu và confirm khi submit
- Ưu tiên: Trung bình

[x] [DASH-UI-008] Hiển thị trạng thái booking rõ ràng
- Đã gắn nhãn màu (badge) nổi bật cho các trạng thái: Pending (vàng), Confirmed (xanh lá), Rejected (đỏ)
- Badge hiển thị rõ ràng, dễ phân biệt trên bảng booking
- Ưu tiên: Cao

[x] [DASH-UI-009] Giao diện cập nhật realtime khi có booking mới (mock socket)
- Đã mô phỏng realtime bằng setInterval, mỗi 15s tự động thêm booking mới và hiển thị thông báo "Có booking mới!"
- Booking mới sẽ có trạng thái pending, hiển thị realtime trên bảng
- Ưu tiên: Thấp

[x] [DASH-UI-010] Responsive UI cho dashboard
- Đã bổ sung responsive, dashboard hiển thị tốt trên tablet/mobile
- Bảng booking có thể scroll ngang trên mobile, filter và button tự động co giãn phù hợp
- Ưu tiên: Trung bình

[x] [DASH-UI-011] Giao diện cấu hình thông tin booking page
- Đã tạo form cấu hình tên, mô tả, ảnh đại diện cho booking page
- Có live preview khi nhập/chọn ảnh
- Lưu thông tin tạm thời trên UI, UX thân thiện
- Ưu tiên: Trung bình

[x] [DASH-UI-012] Cấu hình lịch mở booking (availability setting)
- Đã tạo UI chọn nhiều ngày và khung giờ mở booking (calendar + checkbox slot)
- Lưu và preview lịch đã chọn, giao diện responsive, dễ thao tác
- Ưu tiên: Cao

[ ] [DASH-UI-013] Quản lý loại sân / dịch vụ (field type management)
Agent có thể tạo, sửa, xoá loại sân/dịch vụ có thể đặt
Giao diện list + modal form
Ưu tiên: Trung bình

[ ] [DASH-UI-014] Cấu hình giới hạn booking
Thiết lập số lượng tối đa booking/ngày hoặc /slot
Hiển thị cảnh báo nếu vượt giới hạn
Ưu tiên: Cao

[ ] [DASH-UI-015] Quản lý user đã booking (khách hàng)
Danh sách user từng đặt sân, có lịch sử booking
Hiển thị tên, email, số lần đặt, trạng thái gần nhất
Ưu tiên: Trung bình

[x] [DASH-UI-016] Trang nâng cấp gói đăng ký (subscription plan)
- Đã tạo màn hình nâng cấp gói đăng ký, hiển thị các gói, chọn/nâng cấp (mock)
- Hiển thị gói đang dùng và thời hạn
- Giao diện hiện đại, UX thân thiện
- Ưu tiên: Cao

[x] [DASH-UI-017] Giao diện thanh toán gói đăng ký (mock payment)
Mô phỏng bước checkout thanh toán
Hiển thị popup xác nhận + thông báo thành công
Ưu tiên: Trung bình

[x] [DASH-UI-018] Hiển thị cảnh báo khi vượt quota (booking full, hết gói)
Hiển thị thông báo trên dashboard nếu agent đạt giới hạn
Highlight UI cảnh báo rõ ràng
Ưu tiên: Cao

[x] [DASH-UI-019] Trang cài đặt thông báo (notification settings)
- Nằm trong setting menu
- Agent chọn có nhận thông báo khi có booking mới hay không
- Chọn qua email / dashboard alert / socket
- Ưu tiên: Thấp

[ ] [DASH-UI-020] Giao diện quản lý nhiều page booking (multi-page support)
- Agent có thể tạo nhiều trang booking riêng biệt
- Mỗi trang booking riêng biệt có config riêng
- Có hệ thống quản lý các trang booking
- Xem được lượng booking của mỗi trang
- Có thể add user với các quyền riêng để quản lý
- Danh sách + thao tác nhanh chuyển đổi quản lý từng page
- Ưu tiên: Cao