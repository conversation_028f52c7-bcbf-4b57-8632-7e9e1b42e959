import type { BannerBlock } from '../../types/blocks'
import React from 'react'

export type BannerBlockProps = {
  image: string
  title: string
  subtitle?: string
}

export const BannerBlockComponent: React.FC<BannerBlockProps> = ({
  image,
  title,
  subtitle,
}) => {
  return (
    <div className="w-full overflow-hidden">
      <div
        className="relative w-full h-64 bg-cover bg-center"
        style={{ backgroundImage: `url(${image})` }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col justify-end p-6">
          <h1 className="text-white text-2xl font-bold">{title}</h1>
          {subtitle && (
            <p className="text-white text-sm mt-2">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  )
}

// Config component for the banner block
export const BannerBlockConfig: React.FC<{
  data: BannerBlock['data']
  onChange: (data: BannerBlock['data']) => void
}> = ({ data, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Banner Configuration</h3>

      <div>
        <label className="block text-sm font-medium mb-1">Image URL</label>
        <input
          type="text"
          value={data.image}
          onChange={e => onChange({ ...data, image: e.target.value })}
          className="w-full p-2 border rounded-md"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Title</label>
        <input
          type="text"
          value={data.title}
          onChange={e => onChange({ ...data, title: e.target.value })}
          className="w-full p-2 border rounded-md"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Subtitle (optional)</label>
        <input
          type="text"
          value={data.subtitle || ''}
          onChange={e => onChange({ ...data, subtitle: e.target.value })}
          className="w-full p-2 border rounded-md"
        />
      </div>
    </div>
  )
}
