'use client'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import BookingPageControlContent from '@/modules/admin/components/BookingPageControlPanel/BookingPageControlContent'
import useBookingPageDetail from '@/modules/admin/hooks/useBookingPageDetail'
import { appPaths } from '@/utils/app-routes'
import { AlertCircle, Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'

interface BookingPageDetailScreenProps {
  bookingPageId: string
}

/**
 * Screen component for displaying booking page details
 */
const BookingPageDetailScreen: React.FC<BookingPageDetailScreenProps> = ({ bookingPageId }) => {
  const router = useRouter()
  const { data, isLoading, error, fetchBookingPageDetails } = useBookingPageDetail(bookingPageId)

  // Chuyển hướng đến trang chỉnh sửa booking page
  const handleEditAction = (id: string) => {
    router.push(appPaths.admin.editBookingPage(id))
  }

  // Mở trang booking trong tab mới
  const handleViewLiveAction = (_: string, slug: string) => {
    window.open(`/${slug}`, '_blank')
  }

  // Tải lại dữ liệu khi ID thay đổi
  useEffect(() => {
    if (bookingPageId) {
      fetchBookingPageDetails()
    }
  }, [bookingPageId, fetchBookingPageDetails])

  return (
    <div>
      {error
        ? (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Lỗi</AlertTitle>
              <AlertDescription className="flex flex-col gap-2">
                <p>{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fetchBookingPageDetails()}
                  className="w-fit"
                >
                  Thử lại
                </Button>
              </AlertDescription>
            </Alert>
          )
        : isLoading
          ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                <p className="text-gray-500">Đang tải thông tin booking page...</p>
              </div>
            )
          : (
              <BookingPageControlContent
                bookingPageId={bookingPageId}
                bookingPage={data.bookingPage}
                stats={data.stats}
                bookings={data.bookings}
                onEditAction={handleEditAction}
                onViewLiveAction={handleViewLiveAction}
              />
            )}
    </div>
  )
}

export default BookingPageDetailScreen
