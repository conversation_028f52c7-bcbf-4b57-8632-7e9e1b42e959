import type { UseFormReturn } from 'react-hook-form'
import type { EventConfigFormValues } from '../schema/eventConfigSchema'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import React, { useState } from 'react'

type ColorPickerFieldProps = {
  form: UseFormReturn<EventConfigFormValues>
}

export const ColorPickerField: React.FC<ColorPickerFieldProps> = ({ form }) => {
  const [tempColor, setTempColor] = useState('')

  return (
    <FormField
      control={form.control}
      name="themeColor"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Màu chủ đạo</FormLabel>
          <div className="flex gap-2">
            <FormControl>
              <Input {...field} />
            </FormControl>
            <Input
              type="color"
              value={tempColor || field.value}
              onChange={(e) => {
                setTempColor(e.target.value)
              }}
              onBlur={() => {
                if (tempColor) {
                  field.onChange(tempColor)
                  setTempColor('')
                }
              }}
              className="w-12 h-9 p-1"
            />
          </div>
          <FormDescription>
            Màu chủ đạo sẽ được áp dụng cho các nút và điểm nhấn
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
