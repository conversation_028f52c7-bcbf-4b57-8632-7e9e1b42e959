import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card'
import { AlertCircle, Plus } from 'lucide-react'
import React from 'react'

interface EmptyStateProps {
  /** Whether filters are applied */
  hasFilters: boolean
  /** Handler for create new button */
  onCreateNew: () => void
}

/**
 * Empty state component for when no booking pages are found
 */
export const EmptyState = ({ hasFilters, onCreateNew }: EmptyStateProps) => (
  <Card className="text-center py-8">
    <CardContent>
      <div className="flex flex-col items-center gap-4">
        <div className="rounded-full bg-gray-100 p-4">
          <AlertCircle className="h-8 w-8 text-gray-400" />
        </div>
        <CardTitle className="text-xl">Không tìm thấy booking page nào</CardTitle>
        {hasFilters
          ? (
              <CardDescription>
                Thử thay đổi bộ lọc hoặc tạo mới booking page.
              </CardDescription>
            )
          : (
              <CardDescription>
                Bạn chưa có booking page nào. Hãy tạo mới để bắt đầu.
              </CardDescription>
            )}
        {!hasFilters && (
          <Button onClick={onCreateNew} className="mt-2">
            <Plus className="h-4 w-4 mr-2" />
            Tạo mới booking page
          </Button>
        )}
      </div>
    </CardContent>
  </Card>
)

export default EmptyState
