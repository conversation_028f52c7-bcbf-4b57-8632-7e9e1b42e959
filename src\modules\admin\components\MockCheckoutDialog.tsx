import type { SubscriptionPlan } from '../constants/subscription-plans'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import React, { useState } from 'react'
import { toast } from 'sonner'

const DISCOUNT_CODES = {
  AGENT50: { type: 'percent', value: 50, label: '-50%' },
  AGENT10K: { type: 'amount', value: 10000, label: '-10.000đ' },
}

function applyDiscount(plan: SubscriptionPlan, code: string) {
  const discount = DISCOUNT_CODES[code.toUpperCase() as keyof typeof DISCOUNT_CODES]
  if (!discount) {
    return { price: plan.price, label: plan.priceLabel, applied: false, message: '' }
  }
  let price = plan.price
  let label = ''
  if (discount.type === 'percent') {
    price = Math.max(0, Math.round(plan.price * (1 - discount.value / 100)))
    label = `${price.toLocaleString('vi-VN')}đ (${discount.label})`
  } else if (discount.type === 'amount') {
    price = Math.max(0, plan.price - discount.value)
    label = `${price.toLocaleString('vi-VN')}đ (${discount.label})`
  }
  return { price, label, applied: true, message: `Áp dụng mã giảm giá ${discount.label}` }
}

type MockCheckoutDialogProps = {
  open: boolean
  plan: SubscriptionPlan | null
  onClose: () => void
  onSuccess: (plan: SubscriptionPlan, paid: number, code: string | null) => void
}

export const MockCheckoutDialog: React.FC<MockCheckoutDialogProps> = ({ open, plan, onClose, onSuccess }) => {
  const [discountCode, setDiscountCode] = useState('')
  const [discountMsg, setDiscountMsg] = useState('')
  const [priceInfo, setPriceInfo] = useState<{ price: number, label: string, applied: boolean }>({ price: plan?.price || 0, label: plan?.priceLabel || '', applied: false })
  const [checking, setChecking] = useState(false)

  React.useEffect(() => {
    setDiscountCode('')
    setDiscountMsg('')
    setPriceInfo({ price: plan?.price || 0, label: plan?.priceLabel || '', applied: false })
  }, [plan, open])

  const handleApply = () => {
    if (!discountCode.trim()) {
      return
    }
    setChecking(true)
    setTimeout(() => {
      const result = applyDiscount(plan!, discountCode)
      setPriceInfo(result)
      setDiscountMsg(result.applied ? result.message : 'Mã không hợp lệ!')
      setChecking(false)
    }, 700)
  }

  const handlePay = () => {
    onSuccess(plan!, priceInfo.price, priceInfo.applied ? discountCode : null)
    toast.success('Thanh toán thành công!')
    onClose()
  }

  if (!plan) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Thanh toán nâng cấp gói "
            {plan.name}
            "
          </DialogTitle>
        </DialogHeader>
        <div className="mb-3">
          <div className="text-lg font-semibold text-primary mb-1">
            Giá:
            {priceInfo.label}
          </div>
          <div className="text-gray-600 mb-2">{plan.description}</div>
          <ul className="mb-3 text-sm text-gray-700 list-disc pl-5">
            {plan.features.map(f => <li key={f}>{f}</li>)}
          </ul>
          <div className="flex gap-2 items-center mb-2">
            <Input
              placeholder="Nhập mã giảm giá (AGENT50, AGENT10K)"
              value={discountCode}
              onChange={e => setDiscountCode(e.target.value)}
              className="w-60"
              disabled={checking}
            />
            <Button type="button" variant="outline" onClick={handleApply} disabled={checking}>
              Áp dụng
            </Button>
          </div>
          {discountMsg && (
            <div className={priceInfo.applied ? 'text-green-600 text-sm' : 'text-red-600 text-sm'}>{discountMsg}</div>
          )}
        </div>
        <DialogFooter>
          <Button variant="secondary" onClick={onClose}>Huỷ</Button>
          <Button onClick={handlePay} disabled={checking}>Thanh toán</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MockCheckoutDialog
