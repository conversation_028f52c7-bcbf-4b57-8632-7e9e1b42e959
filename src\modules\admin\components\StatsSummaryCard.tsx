'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { BOOKING_STATUSES } from '@/modules/admin/constants/booking-mock-data'
import React from 'react'

type StatsSummaryCardProps = {
  title: string
  value: number
  description?: string
  change?: number
  status?: keyof typeof BOOKING_STATUSES
}

export const StatsSummaryCard = ({
  title,
  value,
  description,
  change,
  status,
}: StatsSummaryCardProps) => {
  const statusColor = status ? BOOKING_STATUSES[status]?.color : undefined

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {value}
          {status && (
            <span
              className={`ml-2 inline-block h-3 w-3 rounded-full bg-${statusColor}-500`}
            />
          )}
        </div>
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
        {typeof change === 'number' && (
          <div className={`mt-1 text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {change >= 0 ? '↑' : '↓'}
            {' '}
            {Math.abs(change)}
            % so với kỳ trước
          </div>
        )}
      </CardContent>
    </Card>
  )
}
