'use client'

import type { BookingStatsItem } from '@/modules/admin/utils/booking-stats'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { BOOKING_STATUSES } from '@/modules/admin/constants/booking-mock-data'
import React from 'react'
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

type BookingBarChartProps = {
  data: BookingStatsItem[]
  title: string
  description?: string
  showLegend?: boolean
}

export const BookingBarChart = ({
  data,
  title,
  description,
  showLegend = true,
}: BookingBarChartProps) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 0,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => {
                  return [`${value} lượt đặt`, name === 'total' ? 'Tổng số' : BOOKING_STATUSES[name as keyof typeof BOOKING_STATUSES]?.label || name]
                }}
              />
              {showLegend && (
                <Legend
                  formatter={(value) => {
                    return value === 'total' ? 'Tổng số' : BOOKING_STATUSES[value as keyof typeof BOOKING_STATUSES]?.label || value
                  }}
                />
              )}
              <Bar dataKey="total" fill="#8884d8" name="total" />
              <Bar dataKey="confirmed" fill="#82ca9d" name="confirmed" />
              <Bar dataKey="pending" fill="#ffc658" name="pending" />
              <Bar dataKey="completed" fill="#0088FE" name="completed" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
