import type { SelectSingleEventHandler } from 'react-day-picker'
import type { CalendarComponentProps } from '../types'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/libs/utils'
import { format, isSameDay } from 'date-fns'
import { vi } from 'date-fns/locale'
import { CalendarIcon } from 'lucide-react'
import React from 'react'
import { useSelectedDate, useSetSelectedDate } from '../store/availabilityCalendarStore'

/**
 * Calendar component for date selection
 */
export const DatePickerComponent: React.FC<CalendarComponentProps> = (props) => {
  const {
    onDateSelect, // Still used for compatibility
    parsedMinDate,
    parsedMaxDate,
    parsedDisabledDates,
    highlightedDates,
    firstDayOfWeek,
  } = props
  const [open, setOpen] = React.useState(false)

  // Get state and actions from store
  const selectedDate = useSelectedDate()
  const setSelectedDate = useSetSelectedDate()

  const handleSelectDate: SelectSingleEventHandler = (value) => {
    setOpen(false)

    // Call both the prop callback and update the store
    onDateSelect && onDateSelect(value)

    // Update the store with the new date (this will also clear bookings)
    if (value) {
      setSelectedDate(value)
    }
  }

  const renderInputCalendar = () => {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-[240px] pl-3 text-left font-normal',
              !selectedDate && 'text-muted-foreground',
            )}
          >
            {selectedDate
              ? (
                  format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: vi })
                )
              : (
                  <span>Pick a date</span>
                )}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleSelectDate}
            disabled={(date) => {
            // Disable dates outside min/max range
              if (parsedMinDate && date < parsedMinDate) {
                return true
              }
              if (parsedMaxDate && date > parsedMaxDate) {
                return true
              }

              // Disable specific dates
              return parsedDisabledDates.some(disabledDate =>
                isSameDay(date, disabledDate),
              )
            }}
            modifiers={{
              highlighted: date =>
                highlightedDates?.some(h =>
                  isSameDay(date, new Date(h.date)),
                ) || false,
            }}
            modifiersClassNames={{
              highlighted: 'bg-blue-100 text-blue-900 font-medium',
            }}
            weekStartsOn={firstDayOfWeek}
            locale={vi}
            className="mx-auto"
          />
        </PopoverContent>
      </Popover>
    )
  }

  return (
    <div className="mb-6">
      {renderInputCalendar()}
    </div>
  )
}
