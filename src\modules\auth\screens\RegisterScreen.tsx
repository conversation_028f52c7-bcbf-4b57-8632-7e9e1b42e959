'use client'

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Env } from '@/libs/Env'
import { GoogleLogin, GoogleOAuthProvider, useGoogleOneTapLogin } from '@react-oauth/google'
import { motion } from 'framer-motion'
import { CheckCircle2, Mail } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'sonner'
import { authAPIs } from '../auth.apis'

type RegisterResponse = {
  token: string
  user: {
    id: string
    email: string
    name: string
  }
  message: string
}

const RegisterScreen = () => {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [registrationSuccess, setRegistrationSuccess] = useState(false)
  const [registeredEmail, setRegisteredEmail] = useState('')
  const router = useRouter()

  // Setup Google One Tap login
  useGoogleOneTapLogin({
    onSuccess: handleGoogleSuccess,
    onError: () => {
      toast.error('Google registration failed')
    },
  })

  async function handleGoogleSuccess(response: any) {
    try {
      const result = await fetch(`${Env.NEXT_PUBLIC_API_URL}/auth/google-signin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idToken: response.credential,
        }),
      })

      const data: RegisterResponse = await result.json()

      if (!result.ok) {
        throw new Error(data.message || 'Registration failed')
      }

      // Store the token
      localStorage.setItem('token', data.token)
      toast.success('Registered with Google successfully!')

      // Check if email verification is needed
      if (data.user?.email) {
        setRegisteredEmail(data.user.email)
        setRegistrationSuccess(true)
      } else {
        router.push('/admin/dashboard')
      }
    } catch (error: any) {
      toast.error(error.message)
    }
  }

  const handleEmailRegister = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate passwords match
    if (password !== confirmPassword) {
      toast.error('Passwords do not match')
      return
    }

    // Validate password strength
    if (password.length < 6) {
      toast.error('Password must be at least 6 characters')
      return
    }

    setIsLoading(true)

    try {
      const payload = {
        name,
        email,
        password,
      }

      // Call the signup API endpoint
      const result = await authAPIs.register(payload)

      if (!result?.status?.success) {
        throw new Error(result?.status.message || 'Registration failed')
      }

      toast.success('Registered successfully!')

      // Show verification message
      setRegisteredEmail(email)
      setRegistrationSuccess(true)
    } catch (error: any) {
      // Handle API errors
      let errorMessage = error.message || 'Registration failed'

      // Check for common error messages
      if (errorMessage.includes('email') && errorMessage.includes('use')) {
        errorMessage = 'Email is already in use'
      } else if (errorMessage.includes('email') && errorMessage.includes('invalid')) {
        errorMessage = 'Invalid email address'
      } else if (errorMessage.includes('password') && errorMessage.includes('weak')) {
        errorMessage = 'Password is too weak'
      }

      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const renderVerificationMessage = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6 text-center"
      >
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle2 className="w-8 h-8 text-green-600" />
        </div>

        <div className="space-y-2">
          <h2 className="text-2xl font-bold tracking-tight">Registration Successful!</h2>
          <p className="text-muted-foreground">Your account has been created.</p>
        </div>

        <Alert className="bg-blue-50 border-blue-200">

          <AlertTitle className="text-blue-800">
            <div className="flex justify-center items-center mx-auto mb-2">
              <Mail className="h-5 w-5 text-blue-600" />
              {' '}
              Verify your email
            </div>

          </AlertTitle>
          <AlertDescription className="text-blue-700">
            <div>
              We've sent a verification link to
              {' '}
              <span className="font-medium">{registeredEmail}</span>
              .
              Please check your inbox and click the link to verify your account.
            </div>
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Didn't receive the email? Check your spam folder or try again in a few minutes.
          </p>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="outline"
              className="border-primary/20"
              onClick={() => setRegistrationSuccess(false)}
            >
              Back to Register
            </Button>
            <Button onClick={() => router.push('/auth/signin')}>
              Go to Login
            </Button>
          </div>
        </div>
      </motion.div>
    )
  }

  const renderRegisterWithGoogle = () => {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="flex justify-center pt-2"
      >
        <div className="scale-110">
          <GoogleLogin
            onSuccess={handleGoogleSuccess}
            onError={() => toast.error('Google registration failed')}
            useOneTap
            theme="filled_blue"
            shape="pill"
            text="signup_with"
          />
        </div>
      </motion.div>
    )
  }

  const renderRegisterEmail = () => {
    return (
      <motion.form
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        onSubmit={handleEmailRegister}
        className="space-y-4"
      >
        <div className="space-y-2">
          <Label htmlFor="name" className="text-sm font-medium">Full Name</Label>
          <Input
            id="name"
            type="text"
            placeholder="Enter your full name"
            value={name}
            onChange={e => setName(e.target.value)}
            required
            className="h-11 px-4 bg-background/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
            className="h-11 px-4 bg-background/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Create a password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            required
            className="h-11 px-4 bg-background/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-sm font-medium">Confirm Password</Label>
          <Input
            id="confirmPassword"
            type="password"
            placeholder="Confirm your password"
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            required
            className="h-11 px-4 bg-background/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
        </div>

        <Button
          type="submit"
          className="w-full h-11 text-base font-medium shadow-lg shadow-primary/20 hover:shadow-primary/10 transition-all duration-300"
          disabled={isLoading}
        >
          {isLoading
            ? (
                <span className="flex items-center gap-2">
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Creating account...
                </span>
              )
            : 'Create Account'}
        </Button>
      </motion.form>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-primary/5 p-4 sm:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="backdrop-blur-sm bg-background/95 shadow-xl border-primary/10 p-6 sm:p-8 space-y-8">
          {registrationSuccess
            ? (
                renderVerificationMessage()
              )
            : (
                <>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="space-y-3 text-center"
                  >
                    <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Create Account</h1>
                    <p className="text-muted-foreground text-sm">Sign up to get started with our platform</p>
                  </motion.div>

                  {renderRegisterWithGoogle()}

                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t border-primary/10" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-3 text-muted-foreground font-medium">
                        Or register with email
                      </span>
                    </div>
                  </div>

                  {renderRegisterEmail()}

                  <div className="text-center text-sm text-muted-foreground mt-4">
                    Already have an account?
                    {' '}
                    <Link href="/auth/signin" className="text-primary hover:underline">
                      Sign in
                    </Link>
                  </div>
                </>
              )}
        </Card>
      </motion.div>
    </div>
  )
}

export const RegisterScreenWrapper = () => {
  return <GoogleOAuthProvider clientId={Env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}><RegisterScreen /></GoogleOAuthProvider>
}

export default RegisterScreenWrapper
