import type { EventConfigFormValues } from '../schema/eventConfigSchema'

export type FieldType = 'text' | 'number' | 'color' | 'payment-options'

export type FieldConfig = {
  name: keyof EventConfigFormValues
  type: FieldType
  label: string
  description?: string
  fullWidth?: boolean
}

/**
 * Configuration array for event booking form fields
 * This array defines all the fields that will be rendered in the form
 */
export const eventFieldsConfig: FieldConfig[] = [
  {
    name: 'themeColor',
    type: 'color',
    label: 'Màu chủ đạo',
    description: '<PERSON>àu chủ đạo sẽ được áp dụng cho các nút và điểm nhấn',
  },
  {
    name: 'banner',
    type: 'text',
    label: 'Ảnh banner',
    description: 'Đường dẫn đến ảnh banner (khuyến nghị tỷ lệ 16:9)',
  },
  {
    name: 'title',
    type: 'text',
    label: 'Tiêu đề sự kiện',
    fullWidth: true,
  },
  {
    name: 'venue',
    type: 'text',
    label: 'Địa điểm',
  },
  {
    name: 'time',
    type: 'text',
    label: 'Thời gian',
    description: 'Định dạng: DD/MM/YYYY HH:MM',
  },
  {
    name: 'price',
    type: 'number',
    label: 'Giá vé (VNĐ)',
  },
  {
    name: 'slots',
    type: 'number',
    label: 'Số lượng chỗ',
  },
  {
    name: 'mapURL',
    type: 'text',
    label: 'URL Google Maps',
    description: 'URL nhúng Google Maps (iframe src)',
    fullWidth: true,
  },
  {
    name: 'paymentOptions',
    type: 'payment-options',
    label: 'Phương thức thanh toán',
    description: 'Chọn các phương thức thanh toán được hỗ trợ',
    fullWidth: true,
  },
]
