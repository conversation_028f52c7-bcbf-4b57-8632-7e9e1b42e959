import { But<PERSON> } from '@/components/ui/button'
import { Plus, Save, Settings } from 'lucide-react'
import React, { memo } from 'react'

interface HeaderSectionProps {
  onOpenSetup: () => void
  onSubmitPage: () => void
  isSubmiting: boolean
  isConfigComplete: boolean
  isEditMode?: boolean
}

/**
 * HeaderSection Component
 * Displays the page header with action buttons
 */
const HeaderSection = memo(({
  onOpenSetup,
  onSubmitPage,
  isSubmiting,
  isConfigComplete,
  isEditMode = false,
}: HeaderSectionProps) => (
  <div className="bg-white border-b px-6 py-3 flex items-center justify-between">
    <h1 className="text-xl font-bold">
      {isEditMode ? 'Chỉnh sửa booking page' : 'Tạo mới booking page'}
    </h1>
    <div className="flex gap-3">
      <Button
        variant="outline"
        size="sm"
        onClick={onOpenSetup}
        className="flex items-center gap-1"
      >
        <Settings className="h-4 w-4" />
        Thiết lập trang booking
      </Button>
      <Button
        variant="default"
        size="sm"
        onClick={onSubmitPage}
        disabled={isSubmiting || !isConfigComplete}
        className="flex items-center gap-1"
      >
        {isEditMode
          ? (
              <>
                <Save className="h-4 w-4" />
                {isSubmiting ? 'Đang lưu...' : 'Lưu thay đổi'}
              </>
            )
          : (
              <>
                <Plus className="h-4 w-4" />
                {isSubmiting ? 'Đang xử lý...' : 'Tạo page'}
              </>
            )}
      </Button>
    </div>
  </div>
))

HeaderSection.displayName = 'HeaderSection'

export default HeaderSection
