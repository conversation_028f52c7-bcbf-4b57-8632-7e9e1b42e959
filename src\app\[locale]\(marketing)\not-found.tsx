'use client'

import NotFoundPage from '@/components/NotFoundPage'
import { CalendarX } from 'lucide-react'
import { useTranslations } from 'next-intl'

export default function MarketingNotFound() {
  const t = useTranslations('NotFound')

  return (
    <NotFoundPage
      Icon={CalendarX}
      primaryActionUrl="/"
      primaryActionTextKey="home_button"
      customTitle={t('booking_title', { fallback: 'Booking Page Not Found' })}
      customDescription={t('booking_description', { fallback: 'The booking page you are looking for does not exist or has been removed. Please check the URL or contact the booking page owner.' })}
    />
  )
}
