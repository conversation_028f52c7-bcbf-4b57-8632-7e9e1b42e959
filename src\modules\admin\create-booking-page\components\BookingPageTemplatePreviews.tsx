import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React from 'react'

// Sport Field Continuous
export function SportFieldContinuousPreview({ selectedDate, setSelectedDate }: { selectedDate: Date | undefined, setSelectedDate: (date: Date | undefined) => void }) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm">
      <div className="mb-4 border-b pb-2">
        <h3 className="font-semibold text-lg">Đặt sân thể thao - Giờ liên tục</h3>
      </div>
      <div className="space-y-4">
        {/* Chọn ngày */}
        <div>
          <Label>Chọn ngày</Label>
          <div className="border rounded p-2 mt-1">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded border mx-auto"
            />
          </div>
        </div>
        {/* Chọn sân */}
        <div>
          <Label>Chọn sân</Label>
          <div className="grid grid-cols-3 gap-2 mt-1">
            <div className="border rounded p-2 text-center cursor-pointer bg-blue-50 border-blue-300">Sân 1</div>
            <div className="border rounded p-2 text-center cursor-pointer">Sân 2</div>
            <div className="border rounded p-2 text-center cursor-pointer">Sân 3</div>
          </div>
        </div>
        {/* Chọn giờ */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Giờ bắt đầu</Label>
            <select className="w-full border rounded p-2 mt-1">
              <option>08:00</option>
              <option>09:00</option>
              <option>10:00</option>
            </select>
          </div>
          <div>
            <Label>Giờ kết thúc</Label>
            <select className="w-full border rounded p-2 mt-1">
              <option>09:00</option>
              <option>10:00</option>
              <option>11:00</option>
            </select>
          </div>
        </div>
        {/* Số người */}
        <div>
          <Label>Số người</Label>
          <Input type="number" defaultValue="2" min="1" className="mt-1" />
        </div>
        <Button className="w-full mt-4">Đặt sân</Button>
      </div>
    </div>
  )
}

// Sport Field Slots
export function SportFieldSlotsPreview({ selectedDate, setSelectedDate }: { selectedDate: Date | undefined, setSelectedDate: (date: Date | undefined) => void }) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm">
      <div className="mb-4 border-b pb-2">
        <h3 className="font-semibold text-lg">Đặt sân thể thao - Theo khung giờ</h3>
      </div>
      <div className="space-y-4">
        {/* Chọn ngày */}
        <div>
          <Label>Chọn ngày</Label>
          <div className="border rounded p-2 mt-1">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded border mx-auto"
            />
          </div>
        </div>
        {/* Chọn sân */}
        <div>
          <Label>Chọn sân</Label>
          <div className="grid grid-cols-3 gap-2 mt-1">
            <div className="border rounded p-2 text-center cursor-pointer">Sân A</div>
            <div className="border rounded p-2 text-center cursor-pointer bg-green-50 border-green-300">Sân B</div>
            <div className="border rounded p-2 text-center cursor-pointer">Sân C</div>
          </div>
        </div>
        {/* Chọn slot thời gian */}
        <div>
          <Label>Chọn khung giờ (30 phút/slot)</Label>
          <div className="grid grid-cols-4 gap-2 mt-1">
            <div className="border rounded p-2 text-center text-xs cursor-pointer">08:00</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer">08:30</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer bg-green-50 border-green-300">09:00</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer bg-green-50 border-green-300">09:30</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer">10:00</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer">10:30</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer">11:00</div>
            <div className="border rounded p-2 text-center text-xs cursor-pointer">11:30</div>
          </div>
        </div>
        {/* Số người */}
        <div>
          <Label>Số người</Label>
          <Input type="number" defaultValue="4" min="1" className="mt-1" />
        </div>
        <Button className="w-full mt-4">Đặt sân</Button>
      </div>
    </div>
  )
}

// Simple Calendar
export function SimpleCalendarPreview({ selectedDate, setSelectedDate }: { selectedDate: Date | undefined, setSelectedDate: (date: Date | undefined) => void }) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm">
      <div className="mb-4 border-b pb-2">
        <h3 className="font-semibold text-lg">Đặt lịch đơn giản</h3>
      </div>
      <div className="space-y-4">
        {/* Chọn ngày */}
        <div>
          <Label>Chọn ngày đặt lịch</Label>
          <div className="border rounded p-2 mt-1">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded border mx-auto"
            />
          </div>
        </div>
        <div className="pt-4 text-center">
          <div className="mb-2 text-sm text-gray-500">
            Ngày đã chọn:
            {selectedDate?.toLocaleDateString()}
          </div>
          <Button className="w-full">Đặt lịch ngay</Button>
        </div>
      </div>
    </div>
  )
}

// Event Booking
export function EventBookingPreview({ selectedDate, setSelectedDate }: { selectedDate: Date | undefined, setSelectedDate: (date: Date | undefined) => void }) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm" style={{ background: 'linear-gradient(to bottom, #ffffff, #f9f9f9)' }}>
      <div className="mb-4 border-b pb-2">
        <h3 className="font-semibold text-lg text-center">Đặt chỗ sự kiện</h3>
        <p className="text-xs text-center text-gray-500">LIGHT-1 Theme</p>
      </div>
      <div className="space-y-4">
        {/* Chọn sự kiện */}
        <div>
          <Label className="font-medium">Chọn sự kiện</Label>
          <select className="w-full border rounded p-2 mt-1 focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none">
            <option>Concert Mùa Hè 2025</option>
            <option>Hội nghị Công nghệ</option>
            <option>Workshop Khởi nghiệp</option>
          </select>
        </div>
        {/* Chọn ngày */}
        <div>
          <Label className="font-medium">Chọn ngày tham gia</Label>
          <div className="border rounded p-2 mt-1 shadow-sm">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded border mx-auto"
            />
          </div>
        </div>
        {/* Số người tham gia */}
        <div>
          <Label className="font-medium">Số người tham gia</Label>
          <div className="flex items-center mt-1">
            <button type="button" className="border rounded-l w-10 h-10 flex items-center justify-center bg-gray-50 hover:bg-gray-100">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              </svg>
            </button>
            <input type="number" className="border-y h-10 w-16 text-center" value="2" readOnly />
            <button type="button" className="border rounded-r w-10 h-10 flex items-center justify-center bg-gray-50 hover:bg-gray-100">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              </svg>
            </button>
          </div>
        </div>
        {/* Chọn vị trí ngồi */}
        <div>
          <Label className="font-medium">Chọn vị trí ngồi</Label>
          <div className="mt-2 border rounded p-3 shadow-sm">
            <div className="mb-3 text-center font-semibold bg-blue-100 py-1 rounded text-blue-800">SÂN KHẤU</div>
            <div className="grid grid-cols-8 gap-1">
              {Array.from({ length: 32 }).fill(0).map((_, i) => (
                <div
                  key={i}
                  className={`w-6 h-6 rounded-sm text-xs flex items-center justify-center cursor-pointer ${i === 12 || i === 13 ? 'bg-blue-200 border border-blue-400' : 'bg-gray-100 border'}`}
                >
                  {String.fromCharCode(65 + Math.floor(i / 8))}
                  {i % 8 + 1}
                </div>
              ))}
            </div>
          </div>
        </div>
        <button type="button" className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded font-medium transition-colors">
          Đặt chỗ ngay
        </button>
      </div>
    </div>
  )
}

// Car Booking
export function CarBookingPreview() {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm">
      <div className="mb-4 border-b pb-2">
        <h3 className="font-semibold text-lg">Đặt xe</h3>
      </div>
      <div className="space-y-4">
        {/* Chọn loại xe */}
        <div>
          <Label>Chọn loại xe</Label>
          <div className="grid grid-cols-3 gap-2 mt-1">
            <div className="border rounded p-2 text-center cursor-pointer">4 chỗ</div>
            <div className="border rounded p-2 text-center cursor-pointer bg-red-50 border-red-300">7 chỗ</div>
            <div className="border rounded p-2 text-center cursor-pointer">16 chỗ</div>
          </div>
        </div>
        {/* Vị trí đón */}
        <div>
          <Label>Vị trí đón</Label>
          <Input placeholder="Nhập địa chỉ đón" defaultValue="123 Nguyễn Huệ, Q1" className="mt-1" />
        </div>
        {/* Điểm đến */}
        <div>
          <Label>Điểm đến</Label>
          <Input placeholder="Nhập địa chỉ đến" defaultValue="Sân bay Tân Sơn Nhất" className="mt-1" />
        </div>
        {/* Thời gian đón */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Ngày đón</Label>
            <Input type="date" className="mt-1" />
          </div>
          <div>
            <Label>Giờ đón</Label>
            <Input type="time" defaultValue="08:30" className="mt-1" />
          </div>
        </div>
        <Button className="w-full mt-4">Đặt xe</Button>
      </div>
    </div>
  )
}

// Default
export function DefaultTemplatePreview() {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm text-center py-8">
      <div className="text-gray-400">Vui lòng chọn template</div>
    </div>
  )
}
