import type { NextFetchEvent, NextRequest } from 'next/server'
import createMiddleware from 'next-intl/middleware'
import { NextResponse } from 'next/server'
import { routing } from './libs/i18nNavigation'
import { Admin<PERSON>uthHandler } from './middleware/auth'
import { SecurityService } from './middleware/security'
import { RouteUtils } from './middleware/utils'
import { AppConfig } from './utils/AppConfig'

// Internationalization middleware
const intlMiddleware = createMiddleware({
  ...routing,
  localeDetection: true,
  localePrefix: AppConfig.localePrefix,
})

// Main middleware function
export default async function middleware(
  request: NextRequest,
  _event: NextFetchEvent,
): Promise<NextResponse> {
  try {
    // 1. Security check with Arcjet
    await SecurityService.checkSecurity(request)

    // 2. Extract pathname
    const path = request.nextUrl.pathname

    // 3. Handle static files
    if (RouteUtils.isStaticFile(path)) {
      return NextResponse.next()
    }

    // 4. Handle admin authentication
    const adminResponse = await AdminAuthHandler.handleAdminRoute(request)
    if (adminResponse) {
      return adminResponse
    }

    // 5. Continue with i18n processing
    return intlMiddleware(request)
  } catch (error) {
    console.error('Middleware error:', error)
    // In case of error, still try to process with i18n middleware
    return intlMiddleware(request)
  }
}

export const config = {
  matcher: [
    '/((?!_next|monitoring|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    '/(api|trpc)(.*)',
    // Match admin routes with subpaths, but not just /admin
    '/:locale?/admin/:path+',
  ],
}
