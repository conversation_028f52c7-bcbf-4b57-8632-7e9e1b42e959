'use client'

import { userAPIs } from '@/apis/user.apis'
import { deleteCookie, getCookie, setCookie } from 'cookies-next'

// Auth token interface
export interface AuthToken {
  auth: {
    token: string
    refreshToken: string
  }
  user: {
    id: string
    name: string
    email: string
  }
}

// Get token from cookies
export const getToken = (): string => {
  const token = getCookie('token')
  return typeof token === 'string' ? token : ''
}

// Set token in cookies
export const setToken = (token: string): void => {
  setCookie('token', token, { maxAge: 60 * 60 * 24 * 30 }) // 30 days
}

// Get full auth token object from cookies
export const getAuthToken = (): AuthToken | null => {
  const authTokenString = getCookie('authToken')
  if (!authTokenString || typeof authTokenString !== 'string') {
    return null
  }

  try {
    return JSON.parse(authTokenString) as AuthToken
  } catch (error) {
    console.error('Error parsing auth token:', error)
    return null
  }
}

// Set full auth token object in cookies
export const setAuthToken = (authToken: AuthToken): void => {
  setCookie('authToken', JSON.stringify(authToken), { maxAge: 60 * 60 * 24 * 30 }) // 30 days
}

// Clear all auth tokens from cookies
export const clearAuthTokens = (): void => {
  deleteCookie('token')
  deleteCookie('authToken')
}

// Remove undefined values from object
export const removeUndefinedFromObject = <T extends Record<string, any>>(
  obj: T,
): T => {
  const result = { ...obj }

  Object.keys(result).forEach((key) => {
    if (result[key] === undefined) {
      delete result[key]
    }
  })

  return result
}
// Check if the current token is valid by calling the profile API
export const isTokenValid = async (): Promise<boolean> => {
  const token = getToken()
  if (!token) {
    return false
  }

  try {
    const result = await userAPIs.getProfile()

    // Check if the response indicates an invalid token
    if (result?.status?.code === 'auth/invalid-token') {
      clearAuthTokens() // Clear invalid tokens
      return false
    }

    return result?.status?.success === true
  } catch (error) {
    console.error('Error validating token:', error)
    // If there's an error (like network error), clear tokens to be safe
    clearAuthTokens()
    return false
  }
}

// Get token only if it's valid (not expired)
export const getValidToken = async (): Promise<string | null> => {
  const token = getToken()
  if (!token) {
    return null
  }

  const valid = await isTokenValid()
  if (!valid) {
    return null
  }

  return token
}
