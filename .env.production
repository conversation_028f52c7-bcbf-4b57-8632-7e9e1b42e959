# FIXME: Configure environment variables for production

# If you need to build a SaaS application with Stripe subscription payment with checkout page, customer portal, webhook, etc.
# You can check out the Next.js Boilerplate SaaS: https://nextjs-boilerplate.com/pro-saas-starter-kit

# Hosting
# Replace by your domain name
# NEXT_PUBLIC_APP_URL=https://example.com

# Sentry DSN
NEXT_PUBLIC_SENTRY_DSN=

######## [BEGIN] SENSITIVE DATA ######## For security reason, don't update the following variables (secret key) directly in this file.
######## Please create a new file named `.env.production.local`, all environment files ending with `.local` won't be tracked by Git.
######## After creating the file, you can add the following variables.
# Arcjet security
# Get your key from https://launch.arcjet.com/Q6eLbRE
# ARCJET_KEY=

# Database
# Using an incorrect DATABASE_URL value, Next.js build will timeout and you will get the following error: "because it took more than 60 seconds"
# DATABASE_URL=postgresql://postgres@localhost:5432/postgres

# Error monitoring
# SENTRY_AUTH_TOKEN=

# Logging ingestion
# LOGTAIL_SOURCE_TOKEN=
######## [END] SENSITIVE DATA

NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-auth-domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-storage-bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-ga-measurement-id

