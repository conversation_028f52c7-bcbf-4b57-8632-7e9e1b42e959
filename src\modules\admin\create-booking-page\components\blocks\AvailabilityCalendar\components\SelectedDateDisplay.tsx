import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import React from 'react'

interface SelectedDateDisplayProps {
  selectedDate: Date
}

/**
 * Selected date display component
 */
export const SelectedDateDisplay: React.FC<SelectedDateDisplayProps> = ({ selectedDate }) => {
  return (
    <div className="mb-4 p-3 bg-blue-50 rounded-md">
      <h3 className="font-medium">
        Lịch trình cho ngày:
        {' '}
        {format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: vi })}
      </h3>
    </div>
  )
}
