'use client'

import { gsap } from 'gsap'
import React, { useEffect, useRef } from 'react'
import { FaChartBar, FaClock, FaLaptopCode, FaMoneyBillWave } from 'react-icons/fa'

export const BenefitsSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const imageRef = useRef<HTMLDivElement>(null)
  const benefitsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate section title
      gsap.from(titleRef.current, {
        y: 50,
        opacity: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
        },
      })

      // Animate image
      gsap.from(imageRef.current, {
        x: -100,
        opacity: 0,
        duration: 1,
        scrollTrigger: {
          trigger: imageRef.current,
          start: 'top 75%',
        },
      })

      // Animate benefits
      gsap.from('.benefit-item', {
        x: 100,
        opacity: 0,
        stagger: 0.2,
        duration: 0.8,
        scrollTrigger: {
          trigger: benefitsRef.current,
          start: 'top 75%',
        },
      })

      // Create a timeline for the counter animation
      const counterTl = gsap.timeline({
        scrollTrigger: {
          trigger: '.stats-container',
          start: 'top 80%',
        },
      })

      // Animate counters
      gsap.utils.toArray('.counter').forEach((counter: any) => {
        const target = Number.parseInt(counter.getAttribute('data-target') || '0', 10)
        counterTl.to(
          counter,
          {
            innerText: target,
            duration: 2,
            snap: { innerText: 1 },
            ease: 'power2.out',
          },
          0,
        )
      })

      // Create hover effect for benefit items
      gsap.utils.toArray('.benefit-item').forEach((item: any) => {
        const icon = item.querySelector('.benefit-icon')
        const tl = gsap.timeline({ paused: true })

        tl.to(icon, {
          scale: 1.2,
          color: '#f97316', // orange-500
          duration: 0.3,
        })

        // @ts-ignore
        item.addEventListener('mouseenter', () => tl.play())
        // @ts-ignore
        item.addEventListener('mouseleave', () => tl.reverse())
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section
      ref={sectionRef}
      className="py-24 bg-gray-50 relative overflow-hidden"
    >
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-20 bg-gradient-to-b from-white to-transparent" />
        <div className="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-white to-transparent" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          ref={titleRef}
          className="text-4xl md:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent"
        >
          Lợi ích khi sử dụng PickSlot
        </div>

        <div className="flex flex-col lg:flex-row items-center gap-12">
          <div
            ref={imageRef}
            className="flex-1 relative w-full"
          >
            <div className="relative z-10 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 p-4">
              <div className="bg-gray-100 h-6 flex items-center px-4 space-x-2 rounded-t-lg">
                <div className="w-3 h-3 rounded-full bg-red-400"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
              </div>
              <div className="p-4 space-y-4">
                <div className="h-10 bg-orange-100 rounded-lg w-3/4"></div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-32 bg-blue-50 rounded-lg"></div>
                  <div className="h-32 bg-green-50 rounded-lg"></div>
                </div>
                <div className="h-24 bg-purple-50 rounded-lg"></div>
                <div className="h-12 bg-orange-50 rounded-lg w-1/2 mx-auto"></div>
              </div>
            </div>

            {/* Animated elements */}
            <div className="z-10 absolute -top-6 -left-6 bg-white p-3 rounded-lg shadow-lg">
              <div className="flex items-center gap-2">
                <FaChartBar className="text-green-500" />
                <span className="text-sm font-medium">
                  +
                  <span className="counter" data-target="64">25</span>
                  % doanh thu
                </span>
              </div>
            </div>
            <div className="z-10 absolute -bottom-6 -right-6 bg-white p-3 rounded-lg shadow-lg">
              <div className="flex items-center gap-2">
                <FaClock className="text-blue-500" />
                <span className="text-sm font-medium">
                  Tiết kiệm
                  <span className="ml-2 counter" data-target="37">10</span>
                  h/tuần
                </span>
              </div>
            </div>
          </div>

          <div
            ref={benefitsRef}
            className="flex-1 space-y-8"
          >
            <div className="benefit-item flex items-start gap-4">
              <div className="benefit-icon p-3 bg-orange-100 rounded-lg text-orange-500">
                <FaClock className="text-2xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">Tiết kiệm thời gian</h3>
                <p className="text-gray-600">
                  Tự động hóa quy trình đặt chỗ giúp bạn tiết kiệm hàng chục giờ mỗi tuần, tập trung vào việc phát triển dịch vụ.
                </p>
              </div>
            </div>

            <div className="benefit-item flex items-start gap-4">
              <div className="benefit-icon p-3 bg-green-100 rounded-lg text-green-500">
                <FaMoneyBillWave className="text-2xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">Tăng doanh thu</h3>
                <p className="text-gray-600">
                  Khách hàng có thể đặt chỗ 24/7, giúp tăng tỷ lệ lấp đầy và tối ưu hóa lịch trình của bạn.
                </p>
              </div>
            </div>

            <div className="benefit-item flex items-start gap-4">
              <div className="benefit-icon p-3 bg-blue-100 rounded-lg text-blue-500">
                <FaChartBar className="text-2xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">Phân tích dữ liệu</h3>
                <p className="text-gray-600">
                  Nắm bắt xu hướng đặt chỗ, thời điểm cao điểm và hành vi khách hàng để đưa ra quyết định kinh doanh tốt hơn.
                </p>
              </div>
            </div>

            <div className="benefit-item flex items-start gap-4">
              <div className="benefit-icon p-3 bg-purple-100 rounded-lg text-purple-500">
                <FaLaptopCode className="text-2xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">Dễ dàng tùy chỉnh</h3>
                <p className="text-gray-600">
                  Tùy chỉnh giao diện, quy trình đặt chỗ và các tùy chọn thanh toán theo nhu cầu cụ thể của doanh nghiệp bạn.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="stats-container grid grid-cols-2 md:grid-cols-4 gap-6 mt-20">
          <div className="bg-white p-6 rounded-xl shadow-md text-center">
            <div className="text-3xl font-bold text-orange-500 mb-2">
              <span className="counter" data-target="10000">0</span>
              +
            </div>
            <p className="text-gray-600">Người dùng</p>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-md text-center">
            <div className="text-3xl font-bold text-blue-500 mb-2">
              <span className="counter" data-target="25000">0</span>
              +
            </div>
            <p className="text-gray-600">Lượt đặt chỗ</p>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-md text-center">
            <div className="text-3xl font-bold text-green-500 mb-2">
              <span className="counter" data-target="98">0</span>
              %
            </div>
            <p className="text-gray-600">Khách hàng hài lòng</p>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-md text-center">
            <div className="text-3xl font-bold text-purple-500 mb-2">
              <span className="counter" data-target="15">0</span>
            </div>
            <p className="text-gray-600">Mẫu giao diện</p>
          </div>
        </div>
      </div>
    </section>
  )
}
