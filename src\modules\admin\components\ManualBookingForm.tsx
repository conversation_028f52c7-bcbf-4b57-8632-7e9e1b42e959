import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { format } from 'date-fns'
import React, { useState } from 'react'

export type ManualBookingData = {
  customerName: string
  customerPhone: string
  bookingTime: string
  pageName: string
  notes?: string
}

type ManualBookingFormProps = {
  onSubmit: (data: ManualBookingData) => void
  onCancel: () => void
  defaultPageName?: string
}

export const ManualBookingForm: React.FC<ManualBookingFormProps> = ({ onSubmit, onCancel, defaultPageName }) => {
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [bookingTime, setBookingTime] = useState(format(new Date(), 'yyyy-MM-dd\'T\'HH:mm'))
  const [pageName, setPageName] = useState(defaultPageName || '')
  const [notes, setNotes] = useState('')
  const [error, setError] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!customerName || !customerPhone || !bookingTime || !pageName) {
      setError('Vui lòng nhập đầy đủ thông tin bắt buộc!')
      return
    }
    setError('')
    onSubmit({ customerName, customerPhone, bookingTime, pageName, notes })
  }

  return (
    <form className="space-y-4 p-2" onSubmit={handleSubmit}>
      <div>
        <label className="block mb-1 font-medium">Tên người nhận *</label>
        <Input value={customerName} onChange={e => setCustomerName(e.target.value)} required />
      </div>
      <div>
        <label className="block mb-1 font-medium">Số điện thoại *</label>
        <Input value={customerPhone} onChange={e => setCustomerPhone(e.target.value)} required />
      </div>
      <div>
        <label className="block mb-1 font-medium">Ngày giờ đặt *</label>
        <Input type="datetime-local" value={bookingTime} onChange={e => setBookingTime(e.target.value)} required />
      </div>
      <div>
        <label className="block mb-1 font-medium">Dịch vụ/Page *</label>
        <Input value={pageName} onChange={e => setPageName(e.target.value)} required />
      </div>
      <div>
        <label className="block mb-1 font-medium">Ghi chú</label>
        <Textarea value={notes} onChange={e => setNotes(e.target.value)} rows={2} />
      </div>
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <div className="flex gap-2 justify-end pt-2">
        <Button type="button" variant="outline" onClick={onCancel}>Huỷ</Button>
        <Button type="submit" variant="default">Tạo booking</Button>
      </div>
    </form>
  )
}
