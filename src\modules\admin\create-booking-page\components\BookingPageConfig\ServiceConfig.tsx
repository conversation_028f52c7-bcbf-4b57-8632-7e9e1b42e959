'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import React, { useState } from 'react'
import { useTemplateConfig } from '../../hooks/useTemplateConfig'
import { BlockConfigRenderer } from '../BlockConfigRenderer'
import ThemeConfigPanel from '../ThemeConfigPanel'

/**
 * ServiceConfig Component
 *
 * A configuration component for service booking templates
 */
const ServiceConfig = () => {
  const {
    templateSelected,
    blocks,
    themeSettings,
    handleBlockChange,
    handleLayoutChange,
    handleColorChange,
    handleSave,
    addBlock,
    removeBlock,
    reorderBlocks,
  } = useTemplateConfig()

  const [activeTab, setActiveTab] = useState('blocks')

  // Service specific settings
  const [serviceType, setServiceType] = useState('spa')
  const [allowMultipleBookings, setAllowMultipleBookings] = useState(false)
  const [requireApproval, setRequireApproval] = useState(false)

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          Cấu hình trang đặt dịch vụ
          {templateSelected.name}
        </CardTitle>
        <CardDescription>
          Điều chỉnh các khối nội dung và giao diện của trang đặt dịch vụ
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="blocks">Nội dung</TabsTrigger>
            <TabsTrigger value="theme">Giao diện</TabsTrigger>
            <TabsTrigger value="settings">Cài đặt dịch vụ</TabsTrigger>
          </TabsList>

          <TabsContent value="blocks">
            <BlockConfigRenderer
              blocks={blocks}
              onBlockChange={handleBlockChange}
              onReorderBlocks={reorderBlocks}
              onAddBlock={addBlock}
              onRemoveBlock={removeBlock}
            />
          </TabsContent>

          <TabsContent value="theme">
            <ThemeConfigPanel
              themeSettings={themeSettings}
              handleColorChange={handleColorChange}
              handleLayoutChange={handleLayoutChange}
            />
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Loại dịch vụ</h3>
                <Select value={serviceType} onValueChange={setServiceType}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn loại dịch vụ" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="spa">Spa & Massage</SelectItem>
                    <SelectItem value="beauty">Làm đẹp</SelectItem>
                    <SelectItem value="healthcare">Chăm sóc sức khỏe</SelectItem>
                    <SelectItem value="consultation">Tư vấn</SelectItem>
                    <SelectItem value="other">Khác</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-3">Thời gian đặt trước tối thiểu (giờ)</h3>
                <Select defaultValue="2">
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn thời gian" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Không giới hạn</SelectItem>
                    <SelectItem value="1">1 giờ</SelectItem>
                    <SelectItem value="2">2 giờ</SelectItem>
                    <SelectItem value="4">4 giờ</SelectItem>
                    <SelectItem value="24">24 giờ</SelectItem>
                    <SelectItem value="48">48 giờ</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="multiple-bookings" className="text-base font-medium">
                      Cho phép đặt nhiều dịch vụ cùng lúc
                    </Label>
                    <p className="text-sm text-gray-500">
                      Khách hàng có thể đặt nhiều dịch vụ trong cùng một lần đặt
                    </p>
                  </div>
                  <Switch
                    id="multiple-bookings"
                    checked={allowMultipleBookings}
                    onCheckedChange={setAllowMultipleBookings}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="require-approval" className="text-base font-medium">
                      Yêu cầu phê duyệt đặt chỗ
                    </Label>
                    <p className="text-sm text-gray-500">
                      Đặt chỗ cần được phê duyệt trước khi xác nhận
                    </p>
                  </div>
                  <Switch
                    id="require-approval"
                    checked={requireApproval}
                    onCheckedChange={setRequireApproval}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <Button type="button" className="w-full mt-6" onClick={handleSave}>
          Lưu cấu hình
        </Button>
      </CardContent>
    </Card>
  )
}

export default ServiceConfig
