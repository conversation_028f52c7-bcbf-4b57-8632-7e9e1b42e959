import type { UseFormReturn } from 'react-hook-form'
import type { EventConfigFormValues } from '../schema/eventConfigSchema'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import React from 'react'
import { PAYMENT_OPTIONS } from '../schema/eventConfigSchema'

type PaymentOptionsFieldProps = {
  form: UseFormReturn<EventConfigFormValues>
}

export const PaymentOptionsField: React.FC<PaymentOptionsFieldProps> = ({ form }) => {
  return (
    <div className="col-span-1 md:col-span-2">
      <FormField
        control={form.control}
        name="paymentOptions"
        render={() => (
          <FormItem>
            <div className="mb-4">
              <FormLabel>Phương thức thanh toán</FormLabel>
              <FormDescription>
                Ch<PERSON><PERSON> các phương thức thanh toán được hỗ trợ
              </FormDescription>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {PAYMENT_OPTIONS.map(option => (
                <FormField
                  key={option.id}
                  control={form.control}
                  name="paymentOptions"
                  render={({ field }) => {
                    return (
                      <FormItem
                        key={option.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              return checked
                                ? field.onChange([...field.value, option.id])
                                : field.onChange(
                                    field.value?.filter(
                                      value => value !== option.id,
                                    ),
                                  )
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {option.label}
                        </FormLabel>
                      </FormItem>
                    )
                  }}
                />
              ))}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
