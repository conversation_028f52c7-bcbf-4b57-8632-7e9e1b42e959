'use client'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { motion } from 'framer-motion'
import { CheckCircle2, XCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { authAPIs } from '../auth.apis'

interface EmailVerificationScreenProps {
  token: string
}

const EmailVerificationScreen = ({ token }: EmailVerificationScreenProps) => {
  const [isVerifying, setIsVerifying] = useState(true)
  const [isSuccess, setIsSuccess] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const router = useRouter()

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        const result = await authAPIs.verifyEmail(token)

        if (result?.status?.success) {
          setIsSuccess(true)
          toast.success('Email verified successfully!')
        } else {
          setIsSuccess(false)
          setErrorMessage(result?.status?.message || 'Email verification failed')
          toast.error(result?.status?.message || 'Email verification failed')
        }
      } catch (error: any) {
        setIsSuccess(false)
        setErrorMessage(error.message || 'Email verification failed')
        toast.error(error.message || 'Email verification failed')
      } finally {
        setIsVerifying(false)
      }
    }

    verifyEmail()
  }, [token])

  const renderVerifyingState = () => {
    return (
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <svg className="animate-spin h-12 w-12 text-primary" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold tracking-tight">Verifying Your Email</h2>
        <p className="text-muted-foreground">Please wait while we verify your email address...</p>
      </div>
    )
  }

  const renderSuccessState = () => {
    return (
      <div className="text-center space-y-6">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle2 className="w-10 h-10 text-green-600" />
        </div>

        <div className="space-y-2">
          <h2 className="text-2xl font-bold tracking-tight">Email Verified!</h2>
          <p className="text-muted-foreground">Your email has been successfully verified.</p>
        </div>

        <Alert className="bg-green-50 border-green-200">
          <AlertTitle className="text-green-800">
            <div className="flex justify-center items-center mx-auto mb-2">
              <CheckCircle2 className="h-5 w-5 text-green-600 mr-2" />
              Account Activated
            </div>
          </AlertTitle>
          <AlertDescription className="text-green-700">
            <div>
              Your account has been activated. You can now sign in to access your account.
            </div>
          </AlertDescription>
        </Alert>

        <div className="pt-4">
          <Button onClick={() => router.push('/auth/signin')} className="w-full">
            Go to Login
          </Button>
        </div>
      </div>
    )
  }

  const renderErrorState = () => {
    return (
      <div className="text-center space-y-6">
        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <XCircle className="w-10 h-10 text-red-600" />
        </div>

        <div className="space-y-2">
          <h2 className="text-2xl font-bold tracking-tight">Verification Failed</h2>
          <p className="text-muted-foreground">We couldn't verify your email address.</p>
        </div>

        <Alert className="bg-red-50 border-red-200">
          <AlertTitle className="text-red-800">
            <div className="flex justify-center items-center mx-auto mb-2">
              <XCircle className="h-5 w-5 text-red-600 mr-2" />
              Error
            </div>
          </AlertTitle>
          <AlertDescription className="text-red-700">
            <div>
              {errorMessage || 'The verification link may be invalid or expired.'}
            </div>
          </AlertDescription>
        </Alert>

        <div className="space-y-4 pt-4">
          <p className="text-sm text-muted-foreground">
            If you're having trouble, you can try signing in and requesting a new verification email.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button variant="outline" onClick={() => router.push('/auth/signup')}>
              Register Again
            </Button>
            <Button onClick={() => router.push('/auth/signin')}>
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-primary/5 p-4 sm:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="backdrop-blur-sm bg-background/95 shadow-xl border-primary/10 p-6 sm:p-8 space-y-8">
          {isVerifying
            ? renderVerifyingState()
            : isSuccess
              ? renderSuccessState()
              : renderErrorState()}
        </Card>
      </motion.div>
    </div>
  )
}

export default EmailVerificationScreen
