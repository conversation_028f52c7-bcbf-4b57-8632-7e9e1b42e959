[x][CBP-UI-001] [create-booking-page] update trang create-booking-page
- Ưu tiên: Cao
- define lại template ui
+ id
+ name
+ image
+ desc
+ templateCode
+ configDefault: object

[][CBP-UI-002] [create-booking-page] Tạo trang booking page loại event
- Ưu tiên: Cao
- define template ui "EVENT_BOOKING"
+ id
+ name: Đặt chỗ sự kiện
+ image
+ desc: Hỗ trợ đặt chỗ cho sự kiện, số người tham gia.
+ templateCode: EVENT_BOOKING
+ configDefault: {
  name: '',
  theme: 'LIGHT-1',
}
- flow: 
1. user chọn mẫu giao diện (template ui)
2. sẽ show ra tempalte ui đó
3. các default value sẽ apply cho BookingPageBasicInfoForm
4. các dữ liệu của BookingPageBasicInfoForm sẽ apply cho template ui thực tế
5. BookingPageForm sẽ là 1 smart component, sẽ nhận vào các props để render ra template ui thực tế
6. trong BookingPageForm sẽ có các dump component tương ứng với từng loại template ui
