import type { BookingPageTemplate } from '../constants/templates'
import React from 'react'

interface TemplateInfoPanelProps {
  template: BookingPageTemplate | undefined
}

const TemplateInfoPanel: React.FC<TemplateInfoPanelProps> = ({ template }) => {
  if (!template) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg border text-center text-gray-400 h-full flex flex-col items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
        <p>Vui lòng chọn một template để xem thông tin</p>
      </div>
    )
  }

  // Xác định màu sắc dựa trên loại template
  const getCategoryColor = () => {
    switch (template.category) {
      case 'event': return 'blue'
      case 'sport': return 'green'
      case 'service': return 'purple'
      case 'transportation': return 'red'
      default: return 'blue'
    }
  }

  const color = getCategoryColor()

  return (
    <div>
      {/* Template image and name */}
      <div className={`p-4 bg-${color}-50 rounded-lg border border-${color}-100 mb-4`}>
        <div className="flex items-center">
          <div className={`mr-3 bg-white p-1 rounded-lg border border-${color}-200 shadow-sm overflow-hidden`}>
            <div className="w-16 h-16 relative">
              <img src={template.image} alt={template.name} className="object-cover w-full h-full rounded" />
            </div>
          </div>
          <div>
            <div className={`text-${color}-700 text-xs font-medium mb-1 px-2 py-0.5 bg-${color}-100 rounded-full inline-block`}>
              {template.category}
            </div>
            <div className="text-lg font-semibold">{template.name}</div>
          </div>
        </div>
      </div>

      {/* Template description */}
      <div className="mb-4">
        <div className="text-sm text-gray-600 mb-2">{template.description}</div>
      </div>

      {/* Template details */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-sm border-b border-gray-100 pb-2">
          <span className="text-gray-500">Số block:</span>
          <span className="font-medium">{template.blocks.length}</span>
        </div>

        <div className="flex justify-between text-sm border-b border-gray-100 pb-2">
          <span className="text-gray-500">Loại:</span>
          <span className="font-medium">{template.category}</span>
        </div>

        <div className="flex justify-between text-sm border-b border-gray-100 pb-2">
          <span className="text-gray-500">Layout:</span>
          <span className="font-medium">{template.theme?.layout || 'vertical'}</span>
        </div>
      </div>

      {/* Template tags */}
      {template.tags.length > 0 && (
        <div>
          <div className="text-sm text-gray-500 mb-2">Tags:</div>
          <div className="flex flex-wrap gap-1">
            {template.tags.map(tag => (
              <span key={tag} className={`px-2 py-1 bg-${color}-50 text-${color}-700 border border-${color}-100 rounded-md text-xs`}>
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default TemplateInfoPanel
