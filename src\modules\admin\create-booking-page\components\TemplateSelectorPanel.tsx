import type { BookingPageTemplate } from '../constants/templates'
import React from 'react'
import TemplateSelector from './TemplateSelector'

interface TemplateSelectorPanelProps {
  templates: BookingPageTemplate[]
  selectedTemplate?: BookingPageTemplate | undefined
  selectedTemplateId: string
  onSelect: (templateId: string) => void
}

const TemplateSelectorPanel: React.FC<TemplateSelectorPanelProps> = ({
  templates,
  selectedTemplateId,
  onSelect,
}) => {
  return (
    <div className="w-full">
      <TemplateSelector
        templates={templates}
        selected={selectedTemplateId}
        onSelect={onSelect}
      />
    </div>
  )
}

export default TemplateSelectorPanel
