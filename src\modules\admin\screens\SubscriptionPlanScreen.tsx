/* eslint-disable no-console */
'use client'

import type { SubscriptionPlan } from '../constants/subscription-plans'
import { Card } from '@/components/ui/card'
import React, { useState } from 'react'
import { toast } from 'sonner'
import MockCheckoutDialog from '../components/MockCheckoutDialog'
import SubscriptionPlanCard from '../components/SubscriptionPlanCard'
import { SUBSCRIPTION_PLANS } from '../constants/subscription-plans'

const MOCK_CURRENT_PLAN_ID = 'free'
const MOCK_PLAN_EXPIRE = '20/05/2025'

const SubscriptionPlanScreen: React.FC = () => {
  const [currentPlanId, setCurrentPlanId] = useState<string>(MOCK_CURRENT_PLAN_ID)
  const [expireDate, setExpireDate] = useState<string>(MOCK_PLAN_EXPIRE)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [open, setOpen] = useState(false)

  const handleSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan)
    setOpen(true)
  }

  const handleCheckoutSuccess = (plan: SubscriptionPlan, paid: number, code: string | null) => {
    console.log('Paid:', paid)
    setCurrentPlanId(plan.id)
    setExpireDate('20/05/2026') // mock extend 1 year
    toast.success(`Đã nâng cấp lên gói ${plan.name}${code ? ` (giảm giá: ${code})` : ''}`)
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-2 sm:px-4 md:px-8">
      <Card className="p-6 mb-8 flex flex-col gap-2">
        <h1 className="text-2xl font-bold mb-2">Nâng cấp gói đăng ký</h1>
        <div className="text-sm text-gray-600">
          Gói hiện tại:
          {' '}
          <span className="font-semibold text-primary">{SUBSCRIPTION_PLANS.find(p => p.id === currentPlanId)?.name}</span>
          {expireDate && (
            <span>
              {' '}
              (hết hạn:
              {expireDate}
              )
            </span>
          )}
        </div>
      </Card>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {SUBSCRIPTION_PLANS.map(plan => (
          <SubscriptionPlanCard
            key={plan.id}
            plan={plan}
            current={plan.id === currentPlanId}
            onSelect={() => handleSelect(plan)}
          />
        ))}
      </div>
      <MockCheckoutDialog
        open={open}
        plan={selectedPlan}
        onClose={() => setOpen(false)}
        onSuccess={handleCheckoutSuccess}
      />
    </div>
  )
}

export default SubscriptionPlanScreen
