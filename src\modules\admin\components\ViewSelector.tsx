'use client'

import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import React from 'react'

export type ViewMode = 'day' | 'week' | 'month'

type ViewSelectorProps = {
  viewMode: ViewMode
  onViewModeChange: (mode: ViewMode) => void
}

export function ViewSelector({ viewMode, onViewModeChange }: ViewSelectorProps) {
  return (
    <div className="inline-flex rounded-md border">
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'rounded-l-md rounded-r-none border-r',
          viewMode === 'day' && 'bg-blue-50 text-blue-600',
        )}
        onClick={() => onViewModeChange('day')}
      >
        Ngày
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'rounded-none border-r',
          viewMode === 'week' && 'bg-blue-50 text-blue-600',
        )}
        onClick={() => onViewModeChange('week')}
      >
        Tuần
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'rounded-l-none rounded-r-md',
          viewMode === 'month' && 'bg-blue-50 text-blue-600',
        )}
        onClick={() => onViewModeChange('month')}
      >
        Tháng
      </Button>
    </div>
  )
}
