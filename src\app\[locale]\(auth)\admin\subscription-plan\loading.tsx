import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'

export default function AuthRootLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-primary/5 p-4 sm:p-8">
      <div className="w-full max-w-md">
        <Card className="backdrop-blur-sm bg-background/95 shadow-xl border-primary/10 p-6 sm:p-8 space-y-8">
          <div className="space-y-3 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Loading
            </h1>
            <p className="text-muted-foreground text-sm">Please wait while we prepare your page...</p>
          </div>

          <div className="space-y-6">
            <div className="space-y-4">
              <Progress value={75} className="h-2" />
              <div className="text-xs text-right text-muted-foreground">
                Loading...
              </div>
            </div>

            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
