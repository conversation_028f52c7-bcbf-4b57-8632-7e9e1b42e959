'use client'

import { Button } from '@/components/ui/button'
import useBookingPageDetail from '@/modules/admin/hooks/useBookingPageDetail'
import { appPaths } from '@/utils/app-routes'
import {
  Calendar,
  Home,
} from 'lucide-react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import React from 'react'

export default function BookingPageLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const params = useParams()
  const bookingPageId = params.id as string
  const { data, isLoading } = useBookingPageDetail(bookingPageId)

  // Get the booking page name from the data
  const bookingPageName = data?.bookingPage?.name || 'Booking Page'

  return (
    <div className="min-h-screen flex flex-col">
      {/* Top navigation */}
      <header className="border-b">
        <div className="container flex h-14 items-center">
          <div className="flex items-center gap-4">
            <Link href={appPaths.admin.manageBookingPages()}>
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                <span className="hidden md:inline">Quản lý booking page</span>
              </Button>
            </Link>
            <span className="text-gray-400">/</span>
            <Link href={appPaths.admin.bookingPagePanel(bookingPageId)}>
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span className="hidden md:inline">{isLoading ? 'Đang tải...' : bookingPageName}</span>
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 bg-gray-50">
        {children}
      </main>
    </div>
  )
}
