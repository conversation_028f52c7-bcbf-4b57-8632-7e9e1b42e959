import type { BookingDetail } from '@/modules/booking/booking.apis'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  User,
  XCircle,
} from 'lucide-react'
import BookingDetailLink from './BookingDetailLink'

interface BookingSummaryCardProps {
  bookingDetail: BookingDetail
  showDetailLink?: boolean
  compact?: boolean
}

/**
 * Compact booking summary card
 * Can be used in emails, notifications, or dashboard
 */
export const BookingSummaryCard: React.FC<BookingSummaryCardProps> = ({
  bookingDetail,
  showDetailLink = true,
  compact = false,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi })
    } catch {
      return dateString
    }
  }

  // Get status info
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'confirmed':
        return {
          label: 'Đã xác nhận',
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle,
        }
      case 'pending':
        return {
          label: 'Chờ xác nhận',
          color: 'bg-yellow-100 text-yellow-800',
          icon: AlertCircle,
        }
      case 'cancelled':
        return {
          label: 'Đã hủy',
          color: 'bg-red-100 text-red-800',
          icon: XCircle,
        }
      default:
        return {
          label: status,
          color: 'bg-gray-100 text-gray-800',
          icon: AlertCircle,
        }
    }
  }

  const statusInfo = getStatusInfo(bookingDetail.status)
  const StatusIcon = statusInfo.icon

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className={compact ? 'pb-3' : ''}>
        <div className="flex items-center justify-between">
          <CardTitle className={compact ? 'text-lg' : 'text-xl'}>
            {bookingDetail.bookingPageName}
          </CardTitle>
          <Badge className={statusInfo.color}>
            <StatusIcon className="w-3 h-3 mr-1" />
            {statusInfo.label}
          </Badge>
        </div>
        <p className="text-sm text-gray-600">
          Mã:
          {' '}
          <span className="font-mono">
            #
            {bookingDetail.id}
          </span>
        </p>
      </CardHeader>

      <CardContent className={compact ? 'pt-0' : ''}>
        <div className="space-y-3">
          {/* Customer Info */}
          <div className="flex items-center text-sm">
            <User className="w-4 h-4 mr-2 text-gray-500" />
            <span>{bookingDetail.customerName}</span>
          </div>

          {/* Booking Slots */}
          <div className="space-y-2">
            {bookingDetail.bookingSlots.map((slot, index) => (
              <div key={index} className="bg-gray-50 rounded p-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1 text-gray-500" />
                    <span className="font-medium">{formatDate(slot.date)}</span>
                  </div>
                  {slot.price && (
                    <span className="font-semibold text-primary">
                      {formatPrice(slot.price)}
                    </span>
                  )}
                </div>
                <div className="flex items-center mt-1 text-xs text-gray-600">
                  <Clock className="w-3 h-3 mr-1" />
                  <span>{slot.time}</span>
                  <span className="mx-2">•</span>
                  <MapPin className="w-3 h-3 mr-1" />
                  <span>{slot.field}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Total Price */}
          {bookingDetail.totalPrice && (
            <div className="flex justify-between items-center pt-2 border-t">
              <span className="font-medium">Tổng cộng:</span>
              <span className="font-bold text-lg text-primary">
                {formatPrice(bookingDetail.totalPrice)}
              </span>
            </div>
          )}

          {/* Detail Link */}
          {showDetailLink && (
            <div className="pt-2">
              <BookingDetailLink
                bookingId={bookingDetail.id}
                variant="outline"
                size="sm"
                className="w-full"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default BookingSummaryCard
