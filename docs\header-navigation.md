# Header Navigation

Header navigation component cho trang home với các link đăng nhập và đăng ký.

## Tính năng

### ✅ Đã hoàn thành:

1. **Header Navigation Component** (`HeaderNavigation.tsx`)
   - Fixed header với backdrop blur
   - <PERSON><PERSON> có thể click để về trang chủ
   - Responsive design cho mobile và desktop
   - GSAP animations cho smooth entrance

2. **Authentication-aware Navigation**
   - **Guest users**: Hi<PERSON><PERSON> thị <PERSON>h<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> đầu
   - **Authenticated users**: Hiển thị Dashboard, Quản lý
   - Tự động detect authentication status từ token

3. **Responsive Design**
   - Mobile: Chỉ hiển thị icons
   - Desktop: Hiển thị cả text và icons
   - Adaptive spacing và padding

4. **Integration với Home Screen**
   - Thêm vào `NewHomeScreen.tsx`
   - Điều chỉnh padding của `HeroSection`
   - Sử dụng `appPaths` cho consistent routing

## Cấu trúc Files

### Components
- `src/modules/home/<USER>/new-home/HeaderNavigation.tsx` - Main header component
- `src/modules/home/<USER>/NewHomeScreen.tsx` - Updated to include header

### Routes
- `src/utils/app-routes.ts` - Updated với auth routes
  - `appPaths.auth.login()` → `/auth/signin`
  - `appPaths.auth.register()` → `/auth/signup`
  - `appPaths.public.home()` → `/`

### Test Page
- `src/app/[locale]/(marketing)/test-header/page.tsx` - Test page cho header

## Cách sử dụng

### 1. Trong Home Screen
Header đã được tự động thêm vào `NewHomeScreen`:

```tsx
return (
  <div ref={mainRef} className="overflow-hidden">
    <HeaderNavigation />
    <HeroSection />
    {/* ... other sections */}
  </div>
)
```

### 2. Authentication Detection
Component tự động detect authentication status:

```tsx
const [isAuthenticated, setIsAuthenticated] = useState(false)

useEffect(() => {
  const token = getToken()
  setIsAuthenticated(!!token)
}, [])
```

### 3. Navigation Handlers
Sử dụng `appPaths` cho consistent routing:

```tsx
const handleLogin = () => {
  router.push(appPaths.auth.login())
}

const handleRegister = () => {
  router.push(appPaths.auth.register())
}
```

## Responsive Behavior

### Mobile (< 768px)
- Logo + icon-only buttons
- Compact spacing
- Essential actions only

### Desktop (≥ 768px)
- Logo + text + icon buttons
- Full spacing
- Complete navigation options

## Authentication States

### Guest User Navigation
```
[Logo] PickSlot    [🔑 Đăng nhập] [👤 Đăng ký] [Bắt đầu]
```

### Authenticated User Navigation
```
[Logo] PickSlot    [⚙️ Dashboard] [👤 Quản lý]
```

## Styling & Animations

### GSAP Animations
- Header slides down from top
- Navigation items stagger in
- Smooth hover transitions

### Design System
- Orange gradient branding
- Consistent with existing UI
- Backdrop blur for modern look
- Shadow for depth

## Testing

### Test Page: `/test-header`
- Simulate login/logout
- Test responsive behavior
- Verify navigation links
- Check animations

### Manual Testing
1. Visit home page
2. Check header appears correctly
3. Test login/register buttons
4. Verify responsive behavior
5. Test with actual authentication

## Integration Notes

### Existing Components
- Không conflict với existing navigation
- Tương thích với auth system hiện tại
- Sử dụng existing UI components

### Performance
- Lightweight component
- Efficient re-renders
- Optimized animations

### Accessibility
- Proper button labels
- Keyboard navigation
- Screen reader friendly

## Future Enhancements

### Có thể thêm:
- User avatar/dropdown menu
- Notifications indicator
- Search functionality
- Language switcher
- Dark mode toggle

### Mobile Improvements
- Hamburger menu cho mobile
- Slide-out navigation
- Touch-friendly interactions
