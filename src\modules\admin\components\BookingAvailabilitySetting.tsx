import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { addDays, format } from 'date-fns'
import React, { useState } from 'react'

const TIME_SLOTS = [
  '07:00 - 08:00',
  '08:00 - 09:00',
  '09:00 - 10:00',
  '10:00 - 11:00',
  '11:00 - 12:00',
  '13:00 - 14:00',
  '14:00 - 15:00',
  '15:00 - 16:00',
  '16:00 - 17:00',
  '17:00 - 18:00',
  '18:00 - 19:00',
  '19:00 - 20:00',
]

export type BookingAvailability = {
  dates: Date[]
  slots: string[]
}

type BookingAvailabilitySettingProps = {
  initialAvailability?: BookingAvailability
  onSubmit: (data: BookingAvailability) => void
}

export const BookingAvailabilitySetting: React.FC<BookingAvailabilitySettingProps> = ({ initialAvailability, onSubmit }) => {
  const [dates, setDates] = useState<Date[]>(initialAvailability?.dates || [])
  const [slots, setSlots] = useState<string[]>(initialAvailability?.slots || [])

  // Chọn ngày (multi-date, simple mock)
  const today = new Date()
  const days = Array.from({ length: 14 }, (_, i) => addDays(today, i))

  const toggleDate = (date: Date) => {
    setDates((prev) => {
      const exists = prev.find(d => d.toDateString() === date.toDateString())
      if (exists) {
        return prev.filter(d => d.toDateString() !== date.toDateString())
      }
      return [...prev, date]
    })
  }

  const toggleSlot = (slot: string) => {
    setSlots(prev => prev.includes(slot) ? prev.filter(s => s !== slot) : [...prev, slot])
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ dates, slots })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card className="p-4">
        <div className="font-semibold mb-2">Chọn ngày mở booking (14 ngày tới)</div>
        <div className="flex flex-wrap gap-2">
          {days.map((date) => {
            const selected = dates.find(d => d.toDateString() === date.toDateString())
            return (
              <Button
                key={date.toISOString()}
                type="button"
                size="sm"
                variant={selected ? 'default' : 'outline'}
                className={selected ? 'bg-blue-500 text-white' : ''}
                onClick={() => toggleDate(date)}
              >
                {format(date, 'dd/MM')}
              </Button>
            )
          })}
        </div>
      </Card>
      <Card className="p-4">
        <div className="font-semibold mb-2">Chọn khung giờ có thể đặt</div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
          {TIME_SLOTS.map(slot => (
            <label key={slot} className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={slots.includes(slot)}
                onChange={() => toggleSlot(slot)}
                className="accent-blue-500"
              />
              <span>{slot}</span>
            </label>
          ))}
        </div>
      </Card>
      <Button type="submit" className="w-full md:w-auto">Lưu cấu hình</Button>
      <Card className="p-4 mt-2">
        <div className="font-semibold mb-2">Lịch đã chọn</div>
        <div className="flex flex-wrap gap-2 mb-2">
          {dates.length === 0 ? 'Chưa chọn ngày' : dates.map(d => <span key={d.toISOString()} className="px-2 py-1 bg-blue-100 rounded text-blue-800 text-xs">{format(d, 'dd/MM/yyyy')}</span>)}
        </div>
        <div className="flex flex-wrap gap-2">
          {slots.length === 0 ? 'Chưa chọn khung giờ' : slots.map(s => <span key={s} className="px-2 py-1 bg-green-100 rounded text-green-800 text-xs">{s}</span>)}
        </div>
      </Card>
    </form>
  )
}

export default BookingAvailabilitySetting
