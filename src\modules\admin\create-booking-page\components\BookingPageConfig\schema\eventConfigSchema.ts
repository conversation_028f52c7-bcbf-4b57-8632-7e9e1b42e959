import { z } from 'zod'

// Define the schema for form validation
export const eventConfigSchema = z.object({
  themeColor: z.string().regex(/^#([A-F0-9]{6}|[A-F0-9]{3})$/i, {
    message: '<PERSON>ui lòng nhập mã màu HEX hợp lệ (ví dụ: #2563eb)',
  }),
  banner: z.string().min(1, {
    message: 'Vui lòng chọn ảnh banner cho sự kiện',
  }),
  title: z.string().min(3, {
    message: 'Tiêu đề sự kiện phải có ít nhất 3 ký tự',
  }).max(100, {
    message: 'Tiêu đề sự kiện không được vượt quá 100 ký tự',
  }),
  venue: z.string().min(3, {
    message: 'Địa điểm phải có ít nhất 3 ký tự',
  }),
  time: z.string().min(1, {
    message: '<PERSON><PERSON> lòng nhập thời gian sự kiện',
  }),
  price: z.number().min(0, {
    message: '<PERSON><PERSON><PERSON> vé không được âm',
  }),
  slots: z.number().min(1, {
    message: 'Số lượng chỗ phải ít nhất là 1',
  }),
  mapURL: z.string().url({
    message: 'Vui lòng nhập URL Google Maps hợp lệ',
  }),
  paymentOptions: z.array(z.string()),
})

// Define the type based on the schema
export type EventConfigFormValues = z.infer<typeof eventConfigSchema>

// Payment options available
export const PAYMENT_OPTIONS = [
  // { id: 'Momo', label: 'Momo' },
  // { id: 'ZaloPay', label: 'ZaloPay' },
  // { id: 'ATM', label: 'Thẻ ATM nội địa' },
  // { id: 'VISA', label: 'Thẻ Visa/Mastercard' },
  { id: 'COD', label: 'Thanh toán tại chỗ' },
]
