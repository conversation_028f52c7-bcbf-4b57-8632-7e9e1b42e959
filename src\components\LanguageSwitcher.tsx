'use client'

import { usePathname, useRouter } from '@/libs/i18nNavigation'
import { AppConfig } from '@/utils/AppConfig'
import Cookies from 'js-cookie'
import { useLocale } from 'next-intl'
import { useTransition } from 'react'

const LOCALE_COOKIE_KEY = 'NEXT_LOCALE'

export function LanguageSwitcher() {
  const [isPending, startTransition] = useTransition()
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const handleLocaleChange = (newLocale: string) => {
    // Save locale to cookie
    Cookies.set(LOCALE_COOKIE_KEY, newLocale, { expires: 365 })

    startTransition(() => {
      router.replace(pathname, { locale: newLocale })
    })
  }

  return (
    <div className="relative inline-block text-left">
      <select
        value={locale}
        onChange={e => handleLocaleChange(e.target.value)}
        className="block w-full px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        disabled={isPending}
      >
        {AppConfig.locales.map(loc => (
          <option key={loc} value={loc}>
            {loc.toUpperCase()}
          </option>
        ))}
      </select>
    </div>
  )
}
