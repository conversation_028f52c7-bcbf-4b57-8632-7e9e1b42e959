import TermsScreen from '@/modules/terms/screens/TermsScreen'
import { getTranslations } from 'next-intl/server'

type ITermsProps = {
  params: Promise<{ locale: string }>
}

export async function generateMetadata(props: ITermsProps) {
  const { locale } = await props.params
  const t = await getTranslations({
    locale,
    namespace: 'Terms',
  })

  return {
    title: t('meta_title', { fallback: 'Điều khoản và Điều kiện - PickSlot' }),
    description: t('meta_description', { fallback: 'Điề<PERSON> khoản và điều kiện sử dụng dịch vụ PickSlot' }),
  }
}

export default async function Terms() {
  return <TermsScreen />
}
