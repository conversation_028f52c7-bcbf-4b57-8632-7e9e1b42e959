'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import React, { useState } from 'react'
import { useTemplateConfig } from '../../hooks/useTemplateConfig'
import { BlockConfigRenderer } from '../BlockConfigRenderer'
import ThemeConfigPanel from '../ThemeConfigPanel'

/**
 * TransportationConfig Component
 *
 * A configuration component for transportation booking templates
 */
const TransportationConfig = () => {
  const {
    templateSelected,
    blocks,
    themeSettings,
    handleBlockChange,
    handleLayoutChange,
    handleColorChange,
    handleSave,
    addBlock,
    removeBlock,
    reorderBlocks,
  } = useTemplateConfig()

  const [activeTab, setActiveTab] = useState('blocks')

  // Transportation specific settings
  const [vehicleTypes, setVehicleTypes] = useState(['sedan', 'suv'])
  const [enableLocationPicker, setEnableLocationPicker] = useState(true)

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          Cấu hình trang đặt xe
          {templateSelected.name}
        </CardTitle>
        <CardDescription>
          Điều chỉnh các khối nội dung và giao diện của trang đặt xe
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="blocks">Nội dung</TabsTrigger>
            <TabsTrigger value="theme">Giao diện</TabsTrigger>
            <TabsTrigger value="settings">Cài đặt xe</TabsTrigger>
          </TabsList>

          <TabsContent value="blocks">
            <BlockConfigRenderer
              blocks={blocks}
              onBlockChange={handleBlockChange}
              onReorderBlocks={reorderBlocks}
              onAddBlock={addBlock}
              onRemoveBlock={removeBlock}
            />
          </TabsContent>

          <TabsContent value="theme">
            <ThemeConfigPanel
              themeSettings={themeSettings}
              handleColorChange={handleColorChange}
              handleLayoutChange={handleLayoutChange}
            />
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Loại xe</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="sedan"
                      checked={vehicleTypes.includes('sedan')}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setVehicleTypes([...vehicleTypes, 'sedan'])
                        } else {
                          setVehicleTypes(vehicleTypes.filter(t => t !== 'sedan'))
                        }
                      }}
                      className="rounded"
                    />
                    <label htmlFor="sedan">Sedan (4 chỗ)</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="suv"
                      checked={vehicleTypes.includes('suv')}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setVehicleTypes([...vehicleTypes, 'suv'])
                        } else {
                          setVehicleTypes(vehicleTypes.filter(t => t !== 'suv'))
                        }
                      }}
                      className="rounded"
                    />
                    <label htmlFor="suv">SUV (7 chỗ)</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="van"
                      checked={vehicleTypes.includes('van')}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setVehicleTypes([...vehicleTypes, 'van'])
                        } else {
                          setVehicleTypes(vehicleTypes.filter(t => t !== 'van'))
                        }
                      }}
                      className="rounded"
                    />
                    <label htmlFor="van">Van (16 chỗ)</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="luxury"
                      checked={vehicleTypes.includes('luxury')}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setVehicleTypes([...vehicleTypes, 'luxury'])
                        } else {
                          setVehicleTypes(vehicleTypes.filter(t => t !== 'luxury'))
                        }
                      }}
                      className="rounded"
                    />
                    <label htmlFor="luxury">Xe sang</label>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-3">Thời gian đặt trước tối thiểu (giờ)</h3>
                <Select defaultValue="2">
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn thời gian" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 giờ</SelectItem>
                    <SelectItem value="2">2 giờ</SelectItem>
                    <SelectItem value="4">4 giờ</SelectItem>
                    <SelectItem value="24">24 giờ</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="location-picker" className="text-base font-medium">
                    Bật chọn vị trí trên bản đồ
                  </Label>
                  <p className="text-sm text-gray-500">
                    Cho phép khách hàng chọn vị trí đón/trả trên bản đồ
                  </p>
                </div>
                <Switch
                  id="location-picker"
                  checked={enableLocationPicker}
                  onCheckedChange={setEnableLocationPicker}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <Button type="button" className="w-full mt-6" onClick={handleSave}>
          Lưu cấu hình
        </Button>
      </CardContent>
    </Card>
  )
}

export default TransportationConfig
