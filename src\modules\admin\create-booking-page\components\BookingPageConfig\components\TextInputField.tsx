import type { UseFormReturn } from 'react-hook-form'
import type { EventConfigFormValues } from '../schema/eventConfigSchema'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import React from 'react'

type TextInputFieldProps = {
  form: UseFormReturn<EventConfigFormValues>
  name: keyof EventConfigFormValues
  label: string
  description?: string
  className?: string
  type?: 'text' | 'number'
  fullWidth?: boolean
}

export const TextInputField: React.FC<TextInputFieldProps> = ({
  form,
  name,
  label,
  description,
  className,
  type = 'text',
  fullWidth = false,
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={`${fullWidth ? 'col-span-1 md:col-span-2' : ''} ${className || ''}`}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {type === 'number'
              ? (
                  <Input
                    type="number"
                    {...field}
                    onChange={e => field.onChange(Number(e.target.value))}
                  />
                )
              : (
                  <Input {...field} />
                )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
