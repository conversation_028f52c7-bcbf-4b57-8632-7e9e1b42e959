import type { InfoBlock } from '../../types/blocks'
import React from 'react'

export type InfoBlockProps = {
  time: string
  location: string
  slots: number
  price: number
}

export const InfoBlockComponent: React.FC<InfoBlockProps> = ({
  time,
  location,
  slots,
  price,
}) => {
  // Format price with thousand separator
  const formattedPrice = new Intl.NumberFormat('vi-VN').format(price)

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <h2 className="text-lg font-semibold mb-3">Thông tin sự kiện</h2>
      <div className="space-y-2">
        <div className="flex items-center">
          <span className="text-gray-500 w-24">Thời gian:</span>
          <span className="font-medium">{time}</span>
        </div>
        <div className="flex items-center">
          <span className="text-gray-500 w-24">Đ<PERSON>a điểm:</span>
          <span className="font-medium">{location}</span>
        </div>
        <div className="flex items-center">
          <span className="text-gray-500 w-24">Số lượng:</span>
          <span className="font-medium">
            {slots}
            {' '}
            chỗ
          </span>
        </div>
        <div className="flex items-center">
          <span className="text-gray-500 w-24">Giá vé:</span>
          <span className="font-medium text-green-600">
            {formattedPrice}
            đ
          </span>
        </div>
      </div>
    </div>
  )
}

// Config component for the info block
export const InfoBlockConfig: React.FC<{
  data: InfoBlock['data']
  onChange: (data: InfoBlock['data']) => void
}> = ({ data, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Event Information</h3>

      <div>
        <label className="block text-sm font-medium mb-1">Time</label>
        <input
          type="text"
          value={data.time}
          onChange={e => onChange({ ...data, time: e.target.value })}
          className="w-full p-2 border rounded-md"
          placeholder="DD/MM/YYYY HH:MM"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Location</label>
        <input
          type="text"
          value={data.location}
          onChange={e => onChange({ ...data, location: e.target.value })}
          className="w-full p-2 border rounded-md"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Available Slots</label>
        <input
          type="number"
          value={data.slots}
          onChange={e => onChange({ ...data, slots: Number(e.target.value) })}
          className="w-full p-2 border rounded-md"
          min="1"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Price (VND)</label>
        <input
          type="number"
          value={data.price}
          onChange={e => onChange({ ...data, price: Number(e.target.value) })}
          className="w-full p-2 border rounded-md"
          min="0"
          step="1000"
        />
      </div>
    </div>
  )
}
