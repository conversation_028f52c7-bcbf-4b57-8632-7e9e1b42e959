'use client'

import React, { useState } from 'react'
import NotificationSettingsScreen from './NotificationSettingsScreen'

const SETTINGS = [
  { key: 'notification', label: 'Thông báo' },
  // <PERSON><PERSON> thể mở rộng các loại setting khác ở đây
]

export default function AdminSettingsScreen() {
  const [tab, setTab] = useState('notification')

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-xl font-bold mb-6">Cài đặt tài khoản</h1>
      <div className="flex gap-3 border-b mb-6">
        {SETTINGS.map(s => (
          <button
            key={s.key}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-all focus:outline-none ${tab === s.key ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-primary'}`}
            onClick={() => setTab(s.key)}
            type="button"
          >
            {s.label}
          </button>
        ))}
      </div>
      <div>
        {tab === 'notification' && <NotificationSettingsScreen />}
        {/* Các loại setting khác sẽ render ở đây */}
      </div>
    </div>
  )
}
