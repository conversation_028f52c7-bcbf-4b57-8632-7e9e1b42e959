'use client'

import type { AvailabilityCalendarBlock } from '../../types/blocks'
import type { Field } from './AvailabilityCalendar/types'
import type { FieldWithDynamicPricing } from './AvailabilityCalendarBlockConfig/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import React, { useCallback } from 'react'
import { CommonConfigSection } from './AvailabilityCalendarBlockConfig/CommonConfigSection'
import { IndividualFieldConfig } from './AvailabilityCalendarBlockConfig/IndividualFieldConfig'

/**
 * Component for creating a new field
 */
const createNewField = (fields: Field[]): FieldWithDynamicPricing => {
  return {
    id: `field-${fields.length + 1}`,
    name: `Sân ${fields.length + 1}`,
    type: 'football',
    capacity: 10,
    pricePerHour: 300000,
    dynamicPricing: {
      enabled: false,
      timeBasedPrices: [],
      dayBasedPrices: [],
      combinedRules: [],
    },
  }
}

/**
 * Main component for configuring the availability calendar block
 */
export const AvailabilityCalendarBlockConfig: React.FC<{
  data: AvailabilityCalendarBlock['data']
  onChange: (data: AvailabilityCalendarBlock['data']) => void
}> = ({ data, onChange }) => {
  // Add a new field
  const handleAddField = useCallback(() => {
    const updatedFields = [...(data.fields || [])]
    updatedFields.push(createNewField(updatedFields))

    onChange({
      ...data,
      fields: updatedFields,
    })
  }, [data, onChange])

  // Update field count
  const handleFieldCountChange = useCallback((numFields: number) => {
    if (numFields < 1 || numFields > 10) {
      return
    }

    const currentFields = [...(data.fields || [])]

    // If we need more fields, add them
    while (currentFields.length < numFields) {
      currentFields.push(createNewField(currentFields))
    }

    // If we need fewer fields, remove them
    while (currentFields.length > numFields) {
      currentFields.pop()
    }

    onChange({
      ...data,
      fields: currentFields,
    })
  }, [data, onChange])

  // Update a specific field
  const handleFieldChange = useCallback((index: number, updatedField: Field) => {
    const newFields = [...(data.fields || [])]
    if (newFields[index]) {
      newFields[index] = updatedField
    }
    onChange({ ...data, fields: newFields })
  }, [data, onChange])

  // Update config mode
  const handleConfigModeChange = useCallback((mode: 'common' | 'individual') => {
    onChange({
      ...data,
      configMode: mode,
    })
  }, [data, onChange])

  return (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Cấu hình chung</Label>
        <div className="mt-3 space-y-4">
          <div>
            <Label className="text-sm">Số lượng sân</Label>
            <div className="flex items-center space-x-2 mt-1">
              <Input
                type="number"
                min={1}
                max={10}
                value={data.fields?.length || 1}
                onChange={e => handleFieldCountChange(Number(e.target.value))}
                className="w-20"
              />
              <span className="text-sm text-gray-500">sân</span>
            </div>
          </div>

          <div>
            <Label className="text-sm">Cấu hình sân</Label>
            <RadioGroup
              value={data.configMode || 'common'}
              onValueChange={value => handleConfigModeChange(value as 'common' | 'individual')}
              className="flex flex-col space-y-1 mt-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="common" id="common" />
                <Label htmlFor="common" className="text-sm font-normal">
                  Cấu hình chung cho tất cả sân
                </Label>
              </div>
              {/* <div className="flex items-center space-x-2">
                <RadioGroupItem value="individual" id="individual" />
                <Label htmlFor="individual" className="text-sm font-normal">
                  Cấu hình riêng cho từng sân
                </Label>
              </div> */}
            </RadioGroup>
          </div>

          {/* Common configuration */}
          {(data.configMode === 'common' || !data.configMode) && (
            <CommonConfigSection data={data} onChange={onChange} />
          )}
        </div>
      </div>

      {/* Individual field configuration */}
      {data.configMode === 'individual' && (
        <div>
          <Label className="text-base font-medium">Cấu hình từng sân</Label>
          <div className="mt-3 space-y-4">
            {(data.fields || []).map((field, index) => (
              <IndividualFieldConfig
                key={field.id}
                field={field}
                index={index}
                onChange={updatedField => handleFieldChange(index, updatedField)}
              />
            ))}

            <Button type="button" onClick={handleAddField}>
              Thêm sân
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
