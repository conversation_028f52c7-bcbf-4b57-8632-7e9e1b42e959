'use client'

import type { LucideIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useRouter } from '@/libs/i18nNavigation'
import { cn } from '@/libs/utils'
import gsap from 'gsap'
import { useTranslations } from 'next-intl'
import { useEffect, useRef } from 'react'

type NotFoundPageProps = {
  /**
   * Icon to display on the 404 page
   */
  Icon: LucideIcon
  /**
   * URL to navigate to when clicking the primary action button
   */
  primaryActionUrl: string
  /**
   * Text key for the primary action button (from translations)
   */
  primaryActionTextKey: 'home_button' | 'dashboard_button'
  /**
   * Optional custom icon for the primary action button
   */
  primaryActionIcon?: React.ReactNode
  /**
   * Optional custom title for the not found page
   */
  customTitle?: string
  /**
   * Optional custom description for the not found page
   */
  customDescription?: string
}

export default function NotFoundPage({
  Icon,
  primaryActionUrl,
  primaryActionTextKey,
  primaryActionIcon,
  customTitle,
  customDescription,
}: NotFoundPageProps) {
  const router = useRouter()
  const t = useTranslations('NotFound')
  const containerRef = useRef<HTMLDivElement>(null)
  const iconRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  // Simple entrance animations
  useEffect(() => {
    if (containerRef.current && iconRef.current && contentRef.current) {
      // Create animation timeline with subtle easing
      const tl = gsap.timeline({ defaults: { ease: 'power2.out' } })

      // Animate the container with a simple fade in
      tl.from(containerRef.current, {
        opacity: 0,
        y: 20,
        duration: 0.6,
      })

      // Animate the icon with a subtle entrance
      tl.from(iconRef.current, {
        scale: 0.9,
        opacity: 0,
        duration: 0.5,
      }, '-=0.3')

      // Simple fade for content elements
      tl.from(contentRef.current.children, {
        y: 10,
        opacity: 0,
        stagger: 0.1,
        duration: 0.5,
      }, '-=0.3')
    }
  }, [])

  // Default home icon if not provided
  const defaultHomeIcon = (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  )

  // Default dashboard icon if not provided
  const defaultDashboardIcon = (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect width="7" height="9" x="3" y="3" rx="1" />
      <rect width="7" height="5" x="14" y="3" rx="1" />
      <rect width="7" height="9" x="14" y="12" rx="1" />
      <rect width="7" height="5" x="3" y="16" rx="1" />
    </svg>
  )

  // Back button icon
  const backIcon = (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="m12 19-7-7 7-7" />
      <path d="M19 12H5" />
    </svg>
  )

  // Determine which icon to use for the primary action button
  const actionIcon = primaryActionIcon
    || (primaryActionTextKey === 'home_button' ? defaultHomeIcon : defaultDashboardIcon)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background via-background to-primary/5 p-4 sm:p-8">
      {/* Single subtle decorative element */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 inset-x-0 h-1/3 bg-gradient-to-b from-primary/5 to-transparent" />
      </div>

      {/* Main content card */}
      <Card
        ref={containerRef}
        className="backdrop-blur-sm bg-background/95 shadow-md border-primary/10 p-6 sm:p-8 max-w-md w-full"
      >
        <div className="flex flex-col items-center text-center space-y-6">
          {/* Icon container */}
          <div
            ref={iconRef}
            className="bg-primary/10 p-5 rounded-full"
          >
            <Icon className="h-10 w-10 text-primary-foreground" />
          </div>

          {/* Content section with clean typography */}
          <div ref={contentRef} className="space-y-3">
            <h1 className="text-5xl font-bold tracking-tight text-foreground">
              404
            </h1>
            <h2 className="text-xl font-medium text-foreground">
              {customTitle || t('title', { fallback: 'Page Not Found' })}
            </h2>
            <p className="text-muted-foreground text-sm max-w-md mx-auto">
              {customDescription || t('description', { fallback: 'The page you are looking for doesn\'t exist or has been moved.' })}
            </p>
          </div>

          {/* Buttons with minimal animations */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 w-full">
            <Button
              variant="outline"
              className="flex-1 gap-2"
              onClick={() => router.back()}
            >
              {backIcon}
              {t('back_button', { fallback: 'Go Back' })}
            </Button>
            <Button
              className={cn(
                'flex-1 gap-2 bg-primary hover:bg-primary/90 text-primary-foreground',
              )}
              onClick={() => router.push(primaryActionUrl)}
            >
              {actionIcon}
              {t(primaryActionTextKey, { fallback: primaryActionTextKey === 'home_button' ? 'Go Home' : 'Dashboard' })}
            </Button>
          </div>

          {/* Help text */}
          <div className="text-xs text-muted-foreground pt-2">
            <p>
              {t('help_text', { fallback: 'If you believe this is an error, please contact support.' })}
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}
