import { cn } from '@/libs/utils'
import { CheckCircle, Clock, User } from 'lucide-react'
import React from 'react'

interface SlotStatusIndicatorProps {
  status: 'available' | 'pending' | 'confirmed' | 'selected'
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  className?: string
}

/**
 * Reusable component for displaying slot status with consistent styling
 */
export const SlotStatusIndicator: React.FC<SlotStatusIndicatorProps> = ({
  status,
  size = 'md',
  showText = true,
  className,
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'available':
        return {
          icon: (
            <CheckCircle className={cn(
              size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            )}
            />
          ),
          color: 'text-green-700',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          text: 'Còn trống',
        }
      case 'selected':
        return {
          icon: (
            <User className={cn(
              size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            )}
            />
          ),
          color: 'text-blue-700',
          bgColor: 'bg-blue-100',
          borderColor: 'border-blue-300',
          text: 'Đã chọn',
        }
      case 'pending':
        return {
          icon: (
            <Clock className={cn(
              size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            )}
            />
          ),
          color: 'text-yellow-700',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          text: 'Chờ xác nhận',
        }
      case 'confirmed':
        return {
          icon: (
            <CheckCircle className={cn(
              size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
              'fill-current',
            )}
            />
          ),
          color: 'text-red-700',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          text: 'Đã đặt',
        }
      default:
        return {
          icon: (
            <CheckCircle className={cn(
              size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            )}
            />
          ),
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          text: 'Không xác định',
        }
    }
  }

  const config = getStatusConfig()

  return (
    <div className={cn(
      'flex items-center gap-1.5',
      config.color,
      className,
    )}
    >
      <div className={cn(
        'rounded-full border flex items-center justify-center',
        config.bgColor,
        config.borderColor,
        size === 'sm' ? 'w-5 h-5' : size === 'lg' ? 'w-7 h-7' : 'w-6 h-6',
      )}
      >
        {config.icon}
      </div>
      {showText && (
        <span className={cn(
          'font-medium',
          size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-sm' : 'text-xs',
        )}
        >
          {config.text}
        </span>
      )}
    </div>
  )
}
