'use client'

import type { BookingPageConfig } from '../components/BookingPageConfigForm'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import React, { useState } from 'react'
import { toast } from 'sonner'
import { BookingPageConfigForm } from '../components/BookingPageConfigForm'
import { MOCK_BOOKING_PAGES } from '../constants/mock-pages'

const DEFAULT_CONFIG: BookingPageConfig = {
  name: '',
  description: '',
  avatarUrl: '',
}

const BookingPageConfigScreen: React.FC = () => {
  const [selectedPageId, setSelectedPageId] = useState(MOCK_BOOKING_PAGES[0]?.id || '')
  // L<PERSON>u cấu hình cho từng page
  const [pageConfigs, setPageConfigs] = useState<Record<string, BookingPageConfig>>({
    [MOCK_BOOKING_PAGES[0]?.id || '']: {
      name: MOCK_BOOKING_PAGES[0]?.name || '',
      description: 'Đặt sân bóng mini chất lượng cao, phù hợp mọi lứa tuổi.',
      avatarUrl: MOCK_BOOKING_PAGES[0]?.avatarUrl || '',
    },
    [MOCK_BOOKING_PAGES[1]?.id || '']: {
      name: MOCK_BOOKING_PAGES[1]?.name || '',
      description: 'Phòng gym hiện đại, đầy đủ tiện nghi.',
      avatarUrl: MOCK_BOOKING_PAGES[1]?.avatarUrl || '',
    },
    [MOCK_BOOKING_PAGES[2]?.id || '']: {
      name: MOCK_BOOKING_PAGES[2]?.name || '',
      description: 'Tư vấn tài chính chuyên nghiệp.',
      avatarUrl: MOCK_BOOKING_PAGES[2]?.avatarUrl || '',
    },
  })

  const currentConfig = pageConfigs[selectedPageId] || DEFAULT_CONFIG

  const handleSubmit = (data: BookingPageConfig) => {
    setPageConfigs(prev => ({ ...prev, [selectedPageId]: data }))
    toast(`Đã lưu cấu hình cho page: ${MOCK_BOOKING_PAGES.find(p => p.id === selectedPageId)?.name || ''}`)
  }

  return (
    <div className="max-w-2xl mx-auto py-8 px-2 sm:px-4 md:px-8">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">Cấu hình thông tin từng trang booking</h1>
        <div className="mb-6 flex items-center gap-4">
          <Select value={selectedPageId} onValueChange={setSelectedPageId}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Chọn trang booking" />
            </SelectTrigger>
            <SelectContent>
              {MOCK_BOOKING_PAGES.map(page => (
                <SelectItem key={page.id} value={page.id} className="flex gap-2 items-center">
                  <div className="flex gap-2 items-center">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={page.avatarUrl} />
                      <AvatarFallback>{page.name[0]}</AvatarFallback>
                    </Avatar>
                    <span>{page.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <BookingPageConfigForm initialConfig={currentConfig} onSubmit={handleSubmit} />
      </Card>
    </div>
  )
}

export default BookingPageConfigScreen
