import type { AvailabilityCalendarBlock } from '../../../types/blocks'

// Time-based pricing type
export interface TimeBasedPrice {
  startTime: string // HH:MM format
  endTime: string // HH:MM format
  price: number
}

// Day-based pricing type
export interface DayBasedPrice {
  day: number // 0 = Sunday, 1 = Monday, etc.
  price: number
}

// Combined day and time pricing rule
export interface CombinedPricingRule {
  id: string // Unique identifier for the rule
  name: string // Name for the rule (e.g., "Weekday Evening")
  days: number[] // Array of days this rule applies to (0 = Sunday, 1 = Monday, etc.)
  startTime: string // HH:MM format
  endTime: string // HH:MM format
  price: number
}

// Dynamic pricing type
export interface DynamicPricing {
  enabled: boolean
  timeBasedPrices?: TimeBasedPrice[]
  dayBasedPrices?: DayBasedPrice[]
  combinedRules?: CombinedPricingRule[]
}

// Field type
export interface Field {
  id: string
  name: string
  type: string
  capacity: number
  pricePerHour: number
  isIndoor?: boolean // Made optional
  dynamicPricing?: DynamicPricing
}

// Time slot type
export interface TimeSlot {
  time: string
  available: boolean
}

// Booking type
export interface BookingSlot {
  date: Date
  field: string
  time: string
}

// Multiple bookings type
export type Booking = BookingSlot[]

// Props for the main component
export type AvailabilityCalendarProps = AvailabilityCalendarBlock['data']

// Props for the calendar component
export interface CalendarComponentProps {
  selectedDate?: Date // Made optional since we use the store directly
  onDateSelect: (date: Date | undefined) => void
  parsedMinDate?: Date
  parsedMaxDate?: Date
  parsedDisabledDates: Date[]
  highlightedDates: AvailabilityCalendarProps['highlightedDates']
  firstDayOfWeek: 0 | 1
}

// Props for the availability grid component
export interface AvailabilityGridProps {
  timeSlots: TimeSlot[]
  displayFields: Field[]
  selectedDate: Date
  selectedBookings?: Booking // Made optional since we use the store directly
  setSelectedBookings?: (bookings: Booking) => void // Made optional since we use the store directly
  _selectedBookings?: Booking // Added for compatibility with renamed parameter
  _setSelectedBookings?: (bookings: Booking) => void // Added for compatibility with renamed parameter
}

// Props for the booking summary component
export interface BookingSummaryProps {
  booking: Booking // Made optional since we use the store directly
  displayFields: Field[]
}

// Props for the legend component
export interface LegendProps {
  show: boolean
}
