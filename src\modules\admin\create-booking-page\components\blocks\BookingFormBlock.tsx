import type { BookingFormBlock } from '../../types/blocks'
import { Checkbox } from '@/components/ui/checkbox'
import React from 'react'
import { PAYMENT_OPTIONS } from '../BookingPageConfig/schema/eventConfigSchema'

export interface BookingFormBlockProps {
  fields: string[]
  payment_methods: string[]
}

export const BookingFormBlockComponent: React.FC<BookingFormBlockProps> = ({
  fields,
  payment_methods,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <h2 className="text-lg font-semibold mb-3">Đặt vé</h2>

      <form className="space-y-4">
        {fields.includes('name') && (
          <div>
            <label className="block text-sm font-medium mb-1">Họ tên</label>
            <input type="text" className="w-full p-2 border rounded-md" placeholder="Nhập họ tên của bạn" />
          </div>
        )}

        {fields.includes('email') && (
          <div>
            <label className="block text-sm font-medium mb-1">Email</label>
            <input type="email" className="w-full p-2 border rounded-md" placeholder="Nhập email của bạn" />
          </div>
        )}

        {fields.includes('phone') && (
          <div>
            <label className="block text-sm font-medium mb-1">Số điện thoại</label>
            <input type="tel" className="w-full p-2 border rounded-md" placeholder="Nhập số điện thoại của bạn" />
          </div>
        )}

        {fields.includes('quantity') && (
          <div>
            <label className="block text-sm font-medium mb-1">Số lượng vé</label>
            <input type="number" className="w-full p-2 border rounded-md" min="1" defaultValue="1" />
          </div>
        )}

        {payment_methods && payment_methods.length > 0 && (
          <div>
            <label className="block text-sm font-medium mb-2">Phương thức thanh toán</label>
            <div className="space-y-2">
              {payment_methods.map(method => (
                <div key={method} className="flex items-center">
                  <input type="radio" name="payment" id={method} className="mr-2" />
                  <label htmlFor={method}>{method}</label>
                </div>
              ))}
            </div>
          </div>
        )}

        <button type="submit" className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition">
          Đặt vé ngay
        </button>
      </form>
    </div>
  )
}

// Available field options
const FIELD_OPTIONS = [
  { id: 'name', label: 'Full Name' },
  { id: 'email', label: 'Email' },
  { id: 'phone', label: 'Phone Number' },
  { id: 'quantity', label: 'Ticket Quantity' },
]

// Config component for the booking form block
export const BookingFormBlockConfig: React.FC<{
  data: BookingFormBlock['data']
  onChange: (data: BookingFormBlock['data']) => void
}> = ({ data, onChange }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Booking Form Configuration</h3>

      <div>
        <label className="block text-sm font-medium mb-2">Form Fields</label>
        <div className="space-y-2">
          {FIELD_OPTIONS.map(option => (
            <div key={option.id} className="flex items-center">
              <Checkbox
                id={`field-${option.id}`}
                checked={data.fields.includes(option.id)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    onChange({
                      ...data,
                      fields: [...data.fields, option.id],
                    })
                  } else {
                    onChange({
                      ...data,
                      fields: data.fields.filter(id => id !== option.id),
                    })
                  }
                }}
              />
              <label htmlFor={`field-${option.id}`} className="ml-2 text-sm">
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Payment Methods</label>
        <div className="space-y-2">
          {PAYMENT_OPTIONS.map(option => (
            <div key={option.id} className="flex items-center">
              <Checkbox
                id={`payment-${option.id}`}
                checked={data.payment_methods.includes(option.id)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    onChange({
                      ...data,
                      payment_methods: [...data.payment_methods, option.id],
                    })
                  } else {
                    onChange({
                      ...data,
                      payment_methods: data.payment_methods.filter(id => id !== option.id),
                    })
                  }
                }}
              />
              <label htmlFor={`payment-${option.id}`} className="ml-2 text-sm">
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
