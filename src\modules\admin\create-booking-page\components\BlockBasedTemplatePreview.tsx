'use client'

import React from 'react'
import { useBookingPageConfigStore } from '../stores/booking-page-config'
import { BlockRenderer } from './BlockRenderer'

/**
 * BlockBasedTemplatePreview Component
 *
 * Renders a preview of a block-based template
 */
const BlockBasedTemplatePreview: React.FC = () => {
  const { blocks, themeSettings } = useBookingPageConfigStore()

  // Apply theme settings to the preview
  const previewStyle = {
    'fontFamily': themeSettings.fontFamily,
    '--primary-color': themeSettings.primaryColor,
  } as React.CSSProperties

  return (
    <div
      className=""
      style={previewStyle}
    >
      <BlockRenderer blocks={blocks} layout={themeSettings.layout} isPreview={true} />
    </div>
  )
}

export default BlockBasedTemplatePreview
