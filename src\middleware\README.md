# Middleware Architecture

This directory contains the refactored middleware components for the Next.js application.

## Structure

```
src/middleware/
├── index.ts          # Main exports
├── types.ts          # Type definitions and constants
├── utils.ts          # Utility classes (Token, Locale, Route, Cookie)
├── security.ts       # Security service (Arcjet integration)
├── auth.ts           # Admin authentication handler
└── README.md         # This file
```

## Components

### 1. Types (`types.ts`)
- `TokenValidationResult`: Result of token validation
- `LocaleInfo`: Locale extraction information
- Constants: `ADMIN_ROUTE_REGEX`, `STATIC_FILES`, `AUTH_COOKIES`

### 2. Utilities (`utils.ts`)
- `TokenValidator`: Validates tokens via `/user/profile` API
- `LocaleUtils`: Handles locale extraction and login URL creation
- `RouteUtils`: Route checking utilities
- `CookieUtils`: Cookie management utilities

### 3. Security (`security.ts`)
- `SecurityService`: Arcjet bot detection and security checks

### 4. Authentication (`auth.ts`)
- `AdminAuthHandler`: Handles admin route authentication flow

## Flow

1. **Security Check**: Arcjet bot detection
2. **Static Files**: Bypass processing for static files
3. **Admin Authentication**: Token validation for admin routes
4. **i18n Processing**: Continue with internationalization

## Usage

The main middleware (`src/middleware.ts`) orchestrates all these components:

```typescript
export default async function middleware(request: NextRequest) {
  try {
    await SecurityService.checkSecurity(request)

    if (RouteUtils.isStaticFile(path)) {
      return NextResponse.next()
    }

    const adminResponse = await AdminAuthHandler.handleAdminRoute(request)
    if (adminResponse) {
      return adminResponse
    }

    return intlMiddleware(request)
  } catch (error) {
    return intlMiddleware(request)
  }
}
```

## Benefits

- **Separation of Concerns**: Each component has a single responsibility
- **Testability**: Individual components can be unit tested
- **Maintainability**: Easy to modify specific functionality
- **Reusability**: Components can be reused in other parts of the application
- **Type Safety**: Strong typing throughout the middleware stack
