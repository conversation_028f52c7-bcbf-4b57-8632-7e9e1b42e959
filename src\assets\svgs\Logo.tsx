import type { LogoProps } from './type'
import React from 'react'

const Logo: React.FC<LogoProps> = ({
  width = 128,
  height = 128,
  className = '',
  color = '#f84f00',
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 128 128"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      className={className}
      {...props}
    >
      {/* Calendar base */}
      <rect
        x="16"
        y="20"
        width="96"
        height="92"
        rx="12"
        fill="#ffffff"
        stroke={color}
        strokeWidth="4"
      />

      {/* Top bar (calendar header) */}
      <path d="M40,20 h62 a12,12 0 0 1 12,12 v12 h-100 v-12 a12,12 0 0 1 12,-12 z" fill={color} clipPath="inset(0 0 0 0 round 12px 12px 0 0)" />

      {/* <rect
        x="16"
        y="20"
        width="96"
        height="24"
        rx="12"
        ry={12}
        fill="#3B82F6"
      /> */}

      {/* Tabs (calendar rings) */}
      <circle
        cx="36"
        cy="20"
        r="4"
        fill={color}
      />
      <circle
        cx="92"
        cy="20"
        r="4"
        fill={color}
      />

      {/* Dots in top bar */}
      <circle
        cx="28"
        cy="32"
        r="3"
        fill="#F59E0B"
      />
      <circle
        cx="40"
        cy="32"
        r="3"
        fill="#10B981"
      />
      <circle
        cx="52"
        cy="32"
        r="3"
        fill="#EF4444"
      />

      {/* Checkmark box */}
      <rect
        x="44"
        y="60"
        width="40"
        height="40"
        rx="6"
        fill={color}
      />

      {/* Checkmark */}
      <path
        d="M52 80l6 6 18-18"
        stroke="white"
        strokeWidth="4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default Logo
