'use client'

import type { ReactNode } from 'react'
import { motion } from 'framer-motion'

type AnimatedContainerProps = {
  children: ReactNode
  className?: string
  delay?: number
}

export const AnimatedContainer = ({ children, className = '', delay = 0 }: AnimatedContainerProps) => {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      {children}
    </motion.div>
  )
}
