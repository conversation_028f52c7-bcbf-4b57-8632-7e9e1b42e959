'use client'

import type { ReactNode } from 'react'
import { SidebarProvider, SidebarTrigger } from '@/components/ui'
import { useSidebarState } from '@/hooks/use-sidebar-state'
import React, { memo } from 'react'
import AppSidebar from './AppSidebar'

type DashboardLayoutProps = {
  children: ReactNode
}

const DashboardLayout = memo(({ children }: DashboardLayoutProps) => {
  // Đọc trạng thái sidebar từ cookie
  const [isOpen, setIsOpen] = useSidebarState()

  return (
    <SidebarProvider
      defaultOpen={isOpen}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <AppSidebar />

      <main
        className="flex-1 flex flex-col h-screen overflow-hidden"
      >
        {/* Header */}
        <header
          className="h-16 flex items-center px-8 shadow-sm bg-white mb-6"
        >
          <SidebarTrigger />
        </header>
        <section
          className="px-8 pb-8 flex-1 overflow-y-auto"
        >
          {children}
        </section>
      </main>
    </SidebarProvider>
  )
})

DashboardLayout.displayName = 'DashboardLayout'

export default DashboardLayout
