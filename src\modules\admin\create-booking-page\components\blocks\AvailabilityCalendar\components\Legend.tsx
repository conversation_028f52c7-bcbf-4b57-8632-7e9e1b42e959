import type { LegendProps } from '../types'
import { CheckCircle, Clock, User } from 'lucide-react'
import React from 'react'

/**
 * Legend component for displaying availability status
 */
export const Legend: React.FC<LegendProps> = ({ show }) => {
  if (!show) {
    return null
  }

  const legendItems = [
    {
      icon: <CheckCircle className="h-4 w-4 text-green-700" />,
      color: 'bg-green-50 border-green-200',
      textColor: 'text-green-700',
      label: 'Còn trống',
      description: '<PERSON><PERSON> thể đặt',
    },
    {
      icon: <User className="h-4 w-4 text-blue-700" />,
      color: 'bg-blue-100 border-blue-300',
      textColor: 'text-blue-700',
      label: 'Đã chọn',
      description: 'Đang chọn để đặt',
    },
    {
      icon: <Clock className="h-4 w-4 text-yellow-700" />,
      color: 'bg-yellow-50 border-yellow-200',
      textColor: 'text-yellow-700',
      label: 'Ch<PERSON> xác nhận',
      description: '<PERSON>ang chờ xử lý',
    },
    {
      icon: <CheckCircle className="h-4 w-4 text-red-700 fill-current" />,
      color: 'bg-red-50 border-red-200',
      textColor: 'text-red-700',
      label: 'Đã đặt',
      description: 'Không thể đặt',
    },
  ]

  return (
    <div className="mt-6 pt-4 border-t border-gray-200">
      <h4 className="text-sm font-semibold mb-3 text-gray-800">Chú thích trạng thái</h4>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {legendItems.map((item, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={index} className="flex items-center space-x-2">
            <div className={`w-6 h-6 rounded border-2 flex items-center justify-center ${item.color}`}>
              {item.icon}
            </div>
            <div className="flex flex-col">
              <span className={`text-sm font-medium ${item.textColor}`}>
                {item.label}
              </span>
              <span className="text-xs text-gray-500">
                {item.description}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
