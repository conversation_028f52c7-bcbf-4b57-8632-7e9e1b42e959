import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import React from 'react'

/**
 * Loading skeleton component for booking page cards
 */
export const BookingPageCardSkeleton = () => (
  <Card>
    <CardContent className="p-4">
      <div className="flex items-start gap-4">
        <Skeleton className="h-16 w-16 rounded-md" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-5 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
          <div className="flex gap-2 mt-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
    </CardContent>
    <CardFooter className="bg-gray-50 px-4 py-2 flex justify-between">
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-8 w-24" />
    </CardFooter>
  </Card>
)

export default BookingPageCardSkeleton
