import type { TimeSlot } from './types'
import { addHours, format, isSameDay, parse } from 'date-fns'

/**
 * Format price to Vietnamese currency format
 */
export const formatPrice = (price: number): string => {
  return `${new Intl.NumberFormat('vi-VN').format(price)}đ`
}

/**
 * Check if a date is disabled
 */
export const isDateDisabled = (date: Date, disabledDates: string[]): boolean => {
  const dateStr = format(date, 'yyyy-MM-dd')
  return disabledDates.includes(dateStr)
}

/**
 * Check if a date is within business hours
 */
export const isWithinBusinessHours = (
  date: Date,
  businessHours: { start: string, end: string, daysOfWeek: number[] },
): boolean => {
  const day = date.getDay()
  if (!businessHours.daysOfWeek.includes(day)) {
    return false
  }

  const timeStr = format(date, 'HH:mm')
  const startTime = businessHours.start
  const endTime = businessHours.end

  return timeStr >= startTime && timeStr < endTime
}

/**
 * Generate time slots for the selected date
 */
export const generateTimeSlots = (
  selectedDate: Date,
  businessHours: { start: string, end: string, daysOfWeek: number[] },
  timeSlotInterval: number,
  disabledDates: string[],
): TimeSlot[] => {
  const slots: TimeSlot[] = []
  const dayStart = parse(businessHours.start, 'HH:mm', selectedDate)
  const dayEnd = parse(businessHours.end, 'HH:mm', selectedDate)

  let currentSlot = dayStart
  while (currentSlot < dayEnd) {
    const timeStr = format(currentSlot, 'HH:mm')
    const isAvailable = !isDateDisabled(currentSlot, disabledDates)
      && isWithinBusinessHours(currentSlot, businessHours)

    slots.push({
      time: timeStr,
      available: isAvailable,
    })

    currentSlot = addHours(currentSlot, timeSlotInterval / 60)
  }

  return slots
}

/**
 * Check if a date is highlighted
 */
export const isHighlightedDate = (
  date: Date,
  highlightedDates: Array<{ date: string, color?: string, tooltip?: string }> = [],
): boolean => {
  return highlightedDates.some(h => isSameDay(date, new Date(h.date)))
}

/**
 * Get highlight info for a date
 */
export const getHighlightInfo = (
  date: Date,
  highlightedDates: Array<{ date: string, color?: string, tooltip?: string }> = [],
) => {
  const dateStr = format(date, 'yyyy-MM-dd')
  return highlightedDates.find(h => h.date === dateStr)
}
