'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Mail, MessageSquare, Save, Zap } from 'lucide-react'
import React, { useState } from 'react'
import { FaTelegram } from 'react-icons/fa'
import { toast } from 'sonner'

interface BookingPageIntegrationsProps {
  bookingPageId: string
  bookingPage?: BookingPageItem
}

/**
 * Integrations tab for the booking page control panel
 * Allows configuring integrations with external services like Telegram, Email, Zalo
 */
const BookingPageIntegrations: React.FC<BookingPageIntegrationsProps> = ({ bookingPageId }) => {
  const [integrationsTab, setIntegrationsTab] = useState('notifications')

  // Mock data for integrations
  const [notificationSettings, setNotificationSettings] = useState({
    email: {
      enabled: true,
      recipients: '<EMAIL>, <EMAIL>',
      events: ['booking.created', 'booking.cancelled'],
      template: 'default',
    },
    telegram: {
      enabled: false,
      chatId: '',
      botToken: '',
      events: ['booking.created', 'booking.cancelled', 'booking.reminder'],
    },
    zalo: {
      enabled: false,
      oaId: '',
      oaToken: '',
      events: ['booking.created', 'booking.cancelled'],
    },
  })

  const [calendarSettings, setCalendarSettings] = useState({
    googleCalendar: {
      enabled: false,
      calendarId: '',
      refreshToken: '',
    },
    outlookCalendar: {
      enabled: false,
      calendarId: '',
      refreshToken: '',
    },
  })

  const handleEmailChange = (field: string, value: any) => {
    setNotificationSettings(prev => ({
      ...prev,
      email: {
        ...prev.email,
        [field]: value,
      },
    }))
  }

  const handleTelegramChange = (field: string, value: any) => {
    setNotificationSettings(prev => ({
      ...prev,
      telegram: {
        ...prev.telegram,
        [field]: value,
      },
    }))
  }

  const handleZaloChange = (field: string, value: any) => {
    setNotificationSettings(prev => ({
      ...prev,
      zalo: {
        ...prev.zalo,
        [field]: value,
      },
    }))
  }

  const handleGoogleCalendarChange = (field: string, value: any) => {
    setCalendarSettings(prev => ({
      ...prev,
      googleCalendar: {
        ...prev.googleCalendar,
        [field]: value,
      },
    }))
  }

  const handleOutlookCalendarChange = (field: string, value: any) => {
    setCalendarSettings(prev => ({
      ...prev,
      outlookCalendar: {
        ...prev.outlookCalendar,
        [field]: value,
      },
    }))
  }

  const toggleEmailEvent = (event: string) => {
    const newEvents = notificationSettings.email.events.includes(event)
      ? notificationSettings.email.events.filter(e => e !== event)
      : [...notificationSettings.email.events, event]

    handleEmailChange('events', newEvents)
  }

  const toggleTelegramEvent = (event: string) => {
    const newEvents = notificationSettings.telegram.events.includes(event)
      ? notificationSettings.telegram.events.filter(e => e !== event)
      : [...notificationSettings.telegram.events, event]

    handleTelegramChange('events', newEvents)
  }

  const toggleZaloEvent = (event: string) => {
    const newEvents = notificationSettings.zalo.events.includes(event)
      ? notificationSettings.zalo.events.filter(e => e !== event)
      : [...notificationSettings.zalo.events, event]

    handleZaloChange('events', newEvents)
  }

  const handleSave = () => {
    // In a real app, this would save the data to the backend
    // eslint-disable-next-line no-console
    console.log('Saving integration settings for booking page', bookingPageId, {
      notificationSettings,
      calendarSettings,
    })

    // Show success message
    toast.success('Lưu cài đặt tích hợp thành công!')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Tích hợp dịch vụ</h2>
        <Button onClick={handleSave} className="flex items-center gap-1">
          <Save className="h-4 w-4" />
          Lưu thay đổi
        </Button>
      </div>

      <Tabs value={integrationsTab} onValueChange={setIntegrationsTab}>
        <TabsList className="grid grid-cols-2 w-full md:w-[400px]">
          <TabsTrigger value="notifications" className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            Thông báo
          </TabsTrigger>
          <TabsTrigger value="calendars" className="flex items-center gap-1">
            <Zap className="h-4 w-4" />
            Lịch & Đồng bộ
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="mt-6 space-y-6">
          {/* Email Notifications */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Thông báo Email
                  </CardTitle>
                  <CardDescription>
                    Gửi email thông báo khi có đặt lịch mới hoặc thay đổi
                  </CardDescription>
                </div>
                <Switch
                  checked={notificationSettings.email.enabled}
                  onCheckedChange={checked => handleEmailChange('enabled', checked)}
                />
              </div>
            </CardHeader>
            {notificationSettings.email.enabled && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="emailRecipients">Người nhận</Label>
                  <Input
                    id="emailRecipients"
                    placeholder="<EMAIL>, <EMAIL>"
                    value={notificationSettings.email.recipients}
                    onChange={e => handleEmailChange('recipients', e.target.value)}
                  />
                  <p className="text-xs text-gray-500">
                    Danh sách email nhận thông báo, phân cách bằng dấu phẩy
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="emailTemplate">Mẫu email</Label>
                  <Select
                    value={notificationSettings.email.template}
                    onValueChange={value => handleEmailChange('template', value)}
                  >
                    <SelectTrigger id="emailTemplate">
                      <SelectValue placeholder="Chọn mẫu email" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Mẫu mặc định</SelectItem>
                      <SelectItem value="minimal">Mẫu tối giản</SelectItem>
                      <SelectItem value="detailed">Mẫu chi tiết</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Sự kiện gửi email</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {[
                      { id: 'booking.created', label: 'Tạo đặt lịch mới' },
                      { id: 'booking.updated', label: 'Cập nhật đặt lịch' },
                      { id: 'booking.cancelled', label: 'Hủy đặt lịch' },
                      { id: 'booking.confirmed', label: 'Xác nhận đặt lịch' },
                      { id: 'booking.reminder', label: 'Nhắc nhở đặt lịch' },
                    ].map(event => (
                      <div key={event.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`email-event-${event.id}`}
                          checked={notificationSettings.email.events.includes(event.id)}
                          onChange={() => toggleEmailEvent(event.id)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor={`email-event-${event.id}`} className="text-sm">
                          {event.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-2">
                  <Button variant="outline" type="button">
                    Gửi email kiểm tra
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Telegram Notifications */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FaTelegram className="h-5 w-5" />
                    Thông báo Telegram
                  </CardTitle>
                  <CardDescription>
                    Gửi thông báo đến nhóm hoặc kênh Telegram
                  </CardDescription>
                </div>
                <Switch
                  checked={notificationSettings.telegram.enabled}
                  onCheckedChange={checked => handleTelegramChange('enabled', checked)}
                />
              </div>
            </CardHeader>
            {notificationSettings.telegram.enabled && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="telegramBotToken">Bot Token</Label>
                  <Input
                    id="telegramBotToken"
                    placeholder="123456789:ABCdefGhIJKlmNoPQRsTUVwxyZ"
                    value={notificationSettings.telegram.botToken}
                    onChange={e => handleTelegramChange('botToken', e.target.value)}
                    type="password"
                  />
                  <p className="text-xs text-gray-500">
                    Token của Telegram Bot, lấy từ BotFather
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="telegramChatId">Chat ID</Label>
                  <Input
                    id="telegramChatId"
                    placeholder="-100123456789"
                    value={notificationSettings.telegram.chatId}
                    onChange={e => handleTelegramChange('chatId', e.target.value)}
                  />
                  <p className="text-xs text-gray-500">
                    ID của nhóm hoặc kênh Telegram nhận thông báo
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Sự kiện gửi thông báo</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {[
                      { id: 'booking.created', label: 'Tạo đặt lịch mới' },
                      { id: 'booking.updated', label: 'Cập nhật đặt lịch' },
                      { id: 'booking.cancelled', label: 'Hủy đặt lịch' },
                      { id: 'booking.confirmed', label: 'Xác nhận đặt lịch' },
                      { id: 'booking.reminder', label: 'Nhắc nhở đặt lịch' },
                    ].map(event => (
                      <div key={event.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`telegram-event-${event.id}`}
                          checked={notificationSettings.telegram.events.includes(event.id)}
                          onChange={() => toggleTelegramEvent(event.id)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor={`telegram-event-${event.id}`} className="text-sm">
                          {event.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-2">
                  <Button variant="outline" type="button">
                    Gửi tin nhắn kiểm tra
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Zalo Notifications */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <span className="text-blue-600 font-bold">Z</span>
                    Thông báo Zalo
                  </CardTitle>
                  <CardDescription>
                    Gửi thông báo qua Zalo OA (Official Account)
                  </CardDescription>
                </div>
                <Switch
                  checked={notificationSettings.zalo.enabled}
                  onCheckedChange={checked => handleZaloChange('enabled', checked)}
                />
              </div>
            </CardHeader>
            {notificationSettings.zalo.enabled && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="zaloOaId">Zalo OA ID</Label>
                  <Input
                    id="zaloOaId"
                    placeholder="123456789012345678"
                    value={notificationSettings.zalo.oaId}
                    onChange={e => handleZaloChange('oaId', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="zaloOaToken">Zalo OA Token</Label>
                  <Input
                    id="zaloOaToken"
                    placeholder="abcdefghijklmnopqrstuvwxyz123456789"
                    value={notificationSettings.zalo.oaToken}
                    onChange={e => handleZaloChange('oaToken', e.target.value)}
                    type="password"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Sự kiện gửi thông báo</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {[
                      { id: 'booking.created', label: 'Tạo đặt lịch mới' },
                      { id: 'booking.cancelled', label: 'Hủy đặt lịch' },
                      { id: 'booking.confirmed', label: 'Xác nhận đặt lịch' },
                    ].map(event => (
                      <div key={event.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`zalo-event-${event.id}`}
                          checked={notificationSettings.zalo.events.includes(event.id)}
                          onChange={() => toggleZaloEvent(event.id)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <Label htmlFor={`zalo-event-${event.id}`} className="text-sm">
                          {event.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-2">
                  <Button variant="outline" type="button">
                    Gửi tin nhắn kiểm tra
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="calendars" className="mt-6 space-y-6">
          {/* Google Calendar Integration */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Google Calendar</CardTitle>
                  <CardDescription>
                    Đồng bộ đặt lịch với Google Calendar
                  </CardDescription>
                </div>
                <Switch
                  checked={calendarSettings.googleCalendar.enabled}
                  onCheckedChange={checked => handleGoogleCalendarChange('enabled', checked)}
                />
              </div>
            </CardHeader>
            {calendarSettings.googleCalendar.enabled && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="googleCalendarId">Calendar ID</Label>
                  <Input
                    id="googleCalendarId"
                    placeholder="<EMAIL>"
                    value={calendarSettings.googleCalendar.calendarId}
                    onChange={e => handleGoogleCalendarChange('calendarId', e.target.value)}
                  />
                </div>

                <div className="pt-2">
                  <Button type="button" className="w-full">
                    Kết nối với Google Calendar
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Outlook Calendar Integration */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Microsoft Outlook</CardTitle>
                  <CardDescription>
                    Đồng bộ đặt lịch với Microsoft Outlook Calendar
                  </CardDescription>
                </div>
                <Switch
                  checked={calendarSettings.outlookCalendar.enabled}
                  onCheckedChange={checked => handleOutlookCalendarChange('enabled', checked)}
                />
              </div>
            </CardHeader>
            {calendarSettings.outlookCalendar.enabled && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="outlookCalendarId">Calendar ID</Label>
                  <Input
                    id="outlookCalendarId"
                    placeholder="<EMAIL>"
                    value={calendarSettings.outlookCalendar.calendarId}
                    onChange={e => handleOutlookCalendarChange('calendarId', e.target.value)}
                  />
                </div>

                <div className="pt-2">
                  <Button type="button" className="w-full">
                    Kết nối với Outlook Calendar
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default BookingPageIntegrations
