import type { AvailabilityCalendarBlock } from '../../../types/blocks'
import type { CombinedPricingRule } from '../AvailabilityCalendar/types'
import type { FieldWithDynamicPricing } from '../AvailabilityCalendarBlockConfig/types'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React, { useCallback } from 'react'
import { createExampleCombinedRules } from '../AvailabilityCalendar/utils/pricing'

// Days of week mapping for UI display
const DAYS_OF_WEEK = [
  { value: 0, label: 'CN' },
  { value: 1, label: 'T2' },
  { value: 2, label: 'T3' },
  { value: 3, label: 'T4' },
  { value: 4, label: 'T5' },
  { value: 5, label: 'T6' },
  { value: 6, label: 'T7' },
]

/**
 * Component for configuring a single pricing rule
 */
const PricingRuleItem: React.FC<{
  rule: CombinedPricingRule
  onChange: (updatedRule: CombinedPricingRule) => void
  onDelete: () => void
}> = ({ rule, onChange, onDelete }) => {
  // Update a specific field in the rule
  const handleChange = useCallback((field: keyof CombinedPricingRule, value: any) => {
    onChange({ ...rule, [field]: value })
  }, [rule, onChange])

  // Toggle a day in the days array
  const toggleDay = useCallback((dayValue: number, checked: boolean) => {
    const newDays = [...rule.days]

    if (checked) {
      if (!newDays.includes(dayValue)) {
        newDays.push(dayValue)
      }
    } else {
      const dayIndex = newDays.indexOf(dayValue)
      if (dayIndex !== -1) {
        newDays.splice(dayIndex, 1)
      }
    }

    onChange({ ...rule, days: newDays })
  }, [rule, onChange])

  return (
    <div className="p-3 border rounded-md">
      <div className="mb-3">
        <Label className="text-xs mb-1 block">Tên quy tắc</Label>
        <Input
          value={rule.name}
          onChange={e => handleChange('name', e.target.value)}
        />
      </div>

      <div className="grid grid-cols-2 gap-2 mb-3">
        <div>
          <Label className="text-xs mb-1 block">Thời gian bắt đầu</Label>
          <Input
            type="time"
            value={rule.startTime}
            onChange={e => handleChange('startTime', e.target.value)}
          />
        </div>
        <div>
          <Label className="text-xs mb-1 block">Thời gian kết thúc</Label>
          <Input
            type="time"
            value={rule.endTime}
            onChange={e => handleChange('endTime', e.target.value)}
          />
        </div>
      </div>

      <div className="mb-3">
        <Label className="text-xs mb-1 block">Giá (VNĐ)</Label>
        <Input
          type="number"
          value={rule.price}
          onChange={e => handleChange('price', Number(e.target.value))}
        />
      </div>

      <div className="mb-3">
        <Label className="text-xs mb-1 block">Áp dụng cho các ngày</Label>
        <div className="flex flex-wrap gap-2">
          {DAYS_OF_WEEK.map(day => (
            <div key={day.value} className="flex items-center">
              <Checkbox
                id={`day-${rule.id}-${day.value}`}
                checked={rule.days.includes(day.value)}
                onCheckedChange={checked => toggleDay(day.value, !!checked)}
              />
              <Label
                htmlFor={`day-${rule.id}-${day.value}`}
                className="ml-1 text-xs"
              >
                {day.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-2 flex justify-end">
        <Button
          variant="ghost"
          size="sm"
          className="text-red-500 hover:text-red-700"
          onClick={onDelete}
        >
          Xóa
        </Button>
      </div>
    </div>
  )
}

/**
 * Component for managing dynamic pricing rules
 */
const DynamicPricingRules: React.FC<{
  rules: CombinedPricingRule[]
  onChange: (rules: CombinedPricingRule[]) => void
  onAddExampleRules: () => void
}> = ({ rules, onChange, onAddExampleRules }) => {
  // Update a specific rule
  const handleRuleChange = useCallback((index: number, updatedRule: CombinedPricingRule) => {
    const newRules = [...rules]
    newRules[index] = updatedRule
    onChange(newRules)
  }, [rules, onChange])

  // Delete a rule
  const handleDeleteRule = useCallback((index: number) => {
    const newRules = [...rules]
    newRules.splice(index, 1)
    onChange(newRules)
  }, [rules, onChange])

  // Add a new rule
  const handleAddRule = useCallback(() => {
    const newRule: CombinedPricingRule = {
      id: `rule-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      name: 'Quy tắc mới',
      days: [1, 2, 3, 4, 5], // Monday to Friday by default
      startTime: '08:00',
      endTime: '17:00',
      price: 300000,
    }
    onChange([...rules, newRule])
  }, [rules, onChange])

  return (
    <div>
      <Label className="text-sm font-medium mb-2 block">Quy tắc giá theo ngày và giờ</Label>

      {rules.length === 0
        ? (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500 mb-2">Chưa có quy tắc giá nào</p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={onAddExampleRules}
              >
                Thêm quy tắc giá mẫu
              </Button>
            </div>
          )
        : (
            <div className="space-y-4">
              {rules.map((rule, index) => (
                <PricingRuleItem
                  key={rule.id}
                  rule={rule}
                  onChange={updatedRule => handleRuleChange(index, updatedRule)}
                  onDelete={() => handleDeleteRule(index)}
                />
              ))}

              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddRule}
              >
                Thêm quy tắc mới
              </Button>
            </div>
          )}
    </div>
  )
}

/**
 * Component for common configuration settings
 */
export const CommonConfigSection: React.FC<{
  data: AvailabilityCalendarBlock['data']
  onChange: (data: AvailabilityCalendarBlock['data']) => void
}> = ({ data, onChange }) => {
  const commonConfig = data.commonConfig || { pricePerHour: 300000 }
  const dynamicPricing = commonConfig.dynamicPricing || {
    enabled: false,
    timeBasedPrices: [],
    dayBasedPrices: [],
    combinedRules: [],
  }

  // Handle price per hour change
  const handlePriceChange = useCallback((price: number) => {
    onChange({
      ...data,
      fields: data.fields?.map(item => ({
        ...item,
        pricePerHour: price,
      })),
      commonConfig: {
        ...(data.commonConfig || {}),
        pricePerHour: price,
      },
    })
  }, [data, onChange])

  // Handle time slot interval change
  const handleIntervalChange = useCallback((interval: number) => {
    if (interval < 15 || interval > 120) {
      return
    }
    onChange({
      ...data,
      timeSlotInterval: interval,
    })
  }, [data, onChange])

  // Toggle dynamic pricing
  const handleToggleDynamicPricing = useCallback((enabled: boolean) => {
    onChange({
      ...data,
      commonConfig: {
        ...(data.commonConfig || {}),
        dynamicPricing: {
          ...(data.commonConfig?.dynamicPricing || { timeBasedPrices: [], dayBasedPrices: [], combinedRules: [] }),
          enabled,
        },
      },
    })
  }, [data, onChange])

  // Update combined rules
  const handleUpdateCombinedRules = useCallback((combinedRules: CombinedPricingRule[]) => {
    onChange({
      ...data,
      commonConfig: {
        ...(data.commonConfig || {}),
        dynamicPricing: {
          ...(data.commonConfig?.dynamicPricing || {}),
          enabled: true,
          combinedRules,
        },
      },
    })
  }, [data, onChange])

  // Add example pricing rules
  const handleAddPricingRules = useCallback(() => {
    const rules = createExampleCombinedRules()
    onChange({
      ...data,
      commonConfig: {
        ...(data.commonConfig || {}),
        dynamicPricing: {
          ...(data.commonConfig?.dynamicPricing || {}),
          enabled: true,
          combinedRules: rules,
        },
      },
    })
  }, [data, onChange])

  // Apply common config to all fields
  const handleApplyToAllFields = useCallback(() => {
    const commonConfig = data.commonConfig || {
      pricePerHour: 300000,
      dynamicPricing: {
        enabled: false,
        timeBasedPrices: [],
        dayBasedPrices: [],
        combinedRules: [],
      },
    }

    const updatedFields = (data.fields || []).map((field) => {
      const fieldWithDynamicPricing = field as FieldWithDynamicPricing
      return {
        ...field,
        pricePerHour: commonConfig.pricePerHour || field.pricePerHour,
        dynamicPricing: commonConfig.dynamicPricing
          ? { ...commonConfig.dynamicPricing }
          : fieldWithDynamicPricing.dynamicPricing,
      }
    })

    onChange({
      ...data,
      fields: updatedFields,
    })
  }, [data, onChange])

  return (
    <div className="border p-4 rounded-md">
      <Label className="text-sm font-medium">Cấu hình chung</Label>
      <div className="mt-3">
        <div className="grid grid-cols-1 gap-3">
          <div>
            <Label className="text-xs">Giá/slot cơ bản (VNĐ)</Label>
            <Input
              type="number"
              value={commonConfig.pricePerHour || 300000}
              onChange={e => handlePriceChange(Number(e.target.value))}
              className="w-full"
            />
          </div>

          <div>
            <Label className="text-xs">Khoảng thời gian (phút)</Label>
            <div className="flex items-center space-x-2 mt-1">
              <Input
                type="number"
                min={15}
                max={120}
                step={15}
                value={data.timeSlotInterval || 60}
                onChange={e => handleIntervalChange(Number(e.target.value))}
                className="w-20"
              />
              <span className="text-sm text-gray-500">phút</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Khoảng thời gian giữa các khung giờ đặt sân (15, 30, 45, 60, 90 hoặc 120 phút)
            </p>
          </div>

          <div className="border-t pt-3">
            <div className="flex items-center space-x-2 mb-3">
              <Checkbox
                id="enable-dynamic-pricing"
                checked={dynamicPricing.enabled || false}
                onCheckedChange={checked => handleToggleDynamicPricing(!!checked)}
              />
              <Label htmlFor="enable-dynamic-pricing">Bật cấu hình giá theo thời gian</Label>
            </div>

            {dynamicPricing.enabled && (
              <DynamicPricingRules
                rules={dynamicPricing.combinedRules || []}
                onChange={handleUpdateCombinedRules}
                onAddExampleRules={handleAddPricingRules}
              />
            )}
          </div>

          <div className="mt-2">
            <Button
              type="button"
              onClick={handleApplyToAllFields}
            >
              Áp dụng cho tất cả sân
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
