{"name": "next-js-boilerplate", "version": "3.65.2", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "author": "Ixartz (https://github.com/ixartz)", "engines": {"node": ">=20"}, "scripts": {"dev:spotlight": "spotlight-sidecar", "dev:next": "next dev", "dev": "run-p dev:*", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next out coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "check-types": "tsc --noEmit --pretty", "test": "vitest run", "test:e2e": "playwright test", "commit": "cz", "db:generate": "drizzle-kit generate", "db:migrate": "dotenv -c production -- drizzle-kit migrate", "db:studio": "dotenv -c production -- drizzle-kit studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://127.0.0.1:6006 test-storybook", "prepare": "husky"}, "dependencies": {"@arcjet/next": "1.0.0-beta.4", "@hookform/resolvers": "^4.1.3", "@logtail/pino": "^0.5.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.3", "@react-oauth/google": "^0.12.1", "@sentry/nextjs": "^8.55.0", "@spotlightjs/spotlight": "^2.12.0", "@t3-oss/env-nextjs": "^0.12.0", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^5.1.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.41.0", "embla-carousel-react": "^8.5.2", "firebase": "^11.5.0", "framer-motion": "^12.6.3", "gsap": "^3.13.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.486.0", "next": "^15.2.4", "next-intl": "^3.26.5", "next-themes": "^0.4.6", "pg": "^8.14.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "posthog-js": "^1.233.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "react-use": "^17.6.0", "recharts": "^2.15.3", "slugify": "^1.6.6", "sonner": "^2.0.2", "speakingurl": "^14.0.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@commitlint/cz-commitlint": "^19.8.0", "@eslint-react/eslint-plugin": "^1.38.0", "@faker-js/faker": "^9.6.0", "@next/bundle-analyzer": "^15.2.4", "@next/eslint-plugin-next": "^15.2.4", "@percy/cli": "1.30.7", "@percy/playwright": "^1.0.7", "@playwright/test": "^1.51.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^8.6.10", "@storybook/addon-interactions": "^8.6.10", "@storybook/addon-links": "^8.6.10", "@storybook/addon-onboarding": "^8.6.10", "@storybook/blocks": "^8.6.10", "@storybook/nextjs": "^8.6.10", "@storybook/react": "^8.6.10", "@storybook/test": "^8.6.10", "@storybook/test-runner": "^0.22.0", "@tailwindcss/postcss": "^4.0.17", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/lodash": "^4.17.16", "@types/node": "^22.13.13", "@types/pg": "^8.11.11", "@types/react": "^19.0.12", "@types/speakingurl": "^13.0.6", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.9", "@vitest/expect": "^3.0.9", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.30.5", "eslint": "^9.23.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-testing-library": "^7.1.1", "http-server": "^14.1.1", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.5.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "postcss-load-config": "^6.0.1", "rimraf": "^6.0.1", "semantic-release": "^24.2.3", "start-server-and-test": "^2.0.11", "storybook": "^8.6.10", "tailwindcss": "^4.0.17", "ts-node": "^10.9.2", "typescript": "^5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9", "vitest-fail-on-console": "^0.7.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits"}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}}