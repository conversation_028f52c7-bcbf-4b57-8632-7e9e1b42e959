'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Code, Save, Users } from 'lucide-react'
import React, { useState } from 'react'
import { toast } from 'sonner'

interface BookingPageAdvancedSettingsProps {
  bookingPageId: string
  bookingPage?: BookingPageItem
}

/**
 * Advanced Settings tab for the booking page control panel
 * Allows configuring advanced features like webhooks, agents, etc.
 */
const BookingPageAdvancedSettings: React.FC<BookingPageAdvancedSettingsProps> = ({ bookingPageId }) => {
  const [advancedTab, setAdvancedTab] = useState('webhooks')

  // Mock data for advanced settings
  const [webhookSettings, setWebhookSettings] = useState({
    enabled: false,
    url: '',
    secret: '',
    events: ['booking.created', 'booking.updated', 'booking.cancelled'],
  })

  const [agentSettings, setAgentSettings] = useState({
    enableAgentBooking: true,
    requireCustomerInfo: true,
    notifyCustomer: true,
    agents: [
      { id: 'a1', name: 'Nguyễn Văn A', email: '<EMAIL>', role: 'admin' },
      { id: 'a2', name: 'Trần Thị B', email: '<EMAIL>', role: 'agent' },
    ],
  })

  const [customizationSettings, setCustomizationSettings] = useState({
    customCss: '',
    customJs: '',
    customHtml: '',
    enableCustomCode: false,
  })

  const handleWebhookChange = (field: string, value: any) => {
    setWebhookSettings(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleAgentChange = (field: string, value: any) => {
    setAgentSettings(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleCustomizationChange = (field: string, value: any) => {
    setCustomizationSettings(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const toggleWebhookEvent = (event: string) => {
    const newEvents = webhookSettings.events.includes(event)
      ? webhookSettings.events.filter(e => e !== event)
      : [...webhookSettings.events, event]

    handleWebhookChange('events', newEvents)
  }

  const handleSave = () => {
    // In a real app, this would save the data to the backend
    // eslint-disable-next-line no-console
    console.log('Saving advanced settings for booking page', bookingPageId, {
      webhookSettings,
      agentSettings,
      customizationSettings,
    })

    // Show success message using toast instead of alert
    toast.success('Lưu thành công!')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Cài đặt nâng cao</h2>
        <Button onClick={handleSave} className="flex items-center gap-1">
          <Save className="h-4 w-4" />
          Lưu thay đổi
        </Button>
      </div>

      <Tabs value={advancedTab} onValueChange={setAdvancedTab}>
        <TabsList className="grid grid-cols-3 w-full md:w-[400px]">
          <TabsTrigger value="webhooks" className="flex items-center gap-1">
            <Code className="h-4 w-4" />
            Webhooks
          </TabsTrigger>
          <TabsTrigger value="agents" className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            Quản lý Agents
          </TabsTrigger>
          <TabsTrigger value="customization">Tùy chỉnh</TabsTrigger>
        </TabsList>

        <TabsContent value="webhooks" className="mt-6 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cấu hình Webhook</CardTitle>
              <CardDescription>
                Webhook cho phép gửi thông báo tự động đến hệ thống của bạn khi có sự kiện xảy ra
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="webhookEnabled">Kích hoạt Webhook</Label>
                  <p className="text-sm text-gray-500">
                    Gửi thông báo đến URL của bạn khi có sự kiện
                  </p>
                </div>
                <Switch
                  id="webhookEnabled"
                  checked={webhookSettings.enabled}
                  onCheckedChange={checked => handleWebhookChange('enabled', checked)}
                />
              </div>

              {webhookSettings.enabled && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="webhookUrl">URL Webhook</Label>
                    <Input
                      id="webhookUrl"
                      placeholder="https://example.com/webhook"
                      value={webhookSettings.url}
                      onChange={e => handleWebhookChange('url', e.target.value)}
                    />
                    <p className="text-xs text-gray-500">
                      URL này sẽ nhận các thông báo HTTP POST khi có sự kiện xảy ra
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="webhookSecret">Webhook Secret</Label>
                    <Input
                      id="webhookSecret"
                      placeholder="whsec_..."
                      value={webhookSettings.secret}
                      onChange={e => handleWebhookChange('secret', e.target.value)}
                    />
                    <p className="text-xs text-gray-500">
                      Dùng để xác thực các request webhook đến từ hệ thống của chúng tôi
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Sự kiện nhận thông báo</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {[
                        { id: 'booking.created', label: 'Tạo đặt lịch mới' },
                        { id: 'booking.updated', label: 'Cập nhật đặt lịch' },
                        { id: 'booking.cancelled', label: 'Hủy đặt lịch' },
                        { id: 'booking.confirmed', label: 'Xác nhận đặt lịch' },
                        { id: 'booking.reminder', label: 'Nhắc nhở đặt lịch' },
                        { id: 'booking.completed', label: 'Hoàn thành đặt lịch' },
                      ].map(event => (
                        <div key={event.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`event-${event.id}`}
                            checked={webhookSettings.events.includes(event.id)}
                            onChange={() => toggleWebhookEvent(event.id)}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                          <Label htmlFor={`event-${event.id}`} className="text-sm">
                            {event.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="pt-2">
                    <Button variant="outline" type="button" className="w-full">
                      Kiểm tra Webhook
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents" className="mt-6 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quản lý Agents</CardTitle>
              <CardDescription>
                Cho phép nhân viên (agents) tạo và quản lý đặt lịch thay cho khách hàng
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="agentBooking">Cho phép Agent đặt lịch</Label>
                  <p className="text-sm text-gray-500">
                    Agents có thể tạo đặt lịch thay cho khách hàng
                  </p>
                </div>
                <Switch
                  id="agentBooking"
                  checked={agentSettings.enableAgentBooking}
                  onCheckedChange={checked => handleAgentChange('enableAgentBooking', checked)}
                />
              </div>

              {agentSettings.enableAgentBooking && (
                <>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="requireCustomerInfo">Yêu cầu thông tin khách hàng</Label>
                      <p className="text-sm text-gray-500">
                        Bắt buộc nhập thông tin khách hàng khi agent đặt lịch
                      </p>
                    </div>
                    <Switch
                      id="requireCustomerInfo"
                      checked={agentSettings.requireCustomerInfo}
                      onCheckedChange={checked => handleAgentChange('requireCustomerInfo', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notifyCustomer">Thông báo cho khách hàng</Label>
                      <p className="text-sm text-gray-500">
                        Gửi email xác nhận cho khách hàng khi agent đặt lịch
                      </p>
                    </div>
                    <Switch
                      id="notifyCustomer"
                      checked={agentSettings.notifyCustomer}
                      onCheckedChange={checked => handleAgentChange('notifyCustomer', checked)}
                    />
                  </div>

                  <div className="pt-4">
                    <Label>Danh sách Agents</Label>
                    <div className="mt-2 border rounded-md divide-y">
                      {agentSettings.agents.map(agent => (
                        <div key={agent.id} className="p-3 flex items-center justify-between">
                          <div>
                            <div className="font-medium">{agent.name}</div>
                            <div className="text-sm text-gray-500">{agent.email}</div>
                          </div>
                          <Badge variant={agent.role === 'admin' ? 'default' : 'outline'}>
                            {agent.role === 'admin' ? 'Admin' : 'Agent'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="pt-2">
                    <Button type="button" className="w-full">
                      Thêm Agent mới
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customization" className="mt-6 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tùy chỉnh giao diện</CardTitle>
              <CardDescription>
                Thêm mã CSS, JavaScript hoặc HTML tùy chỉnh cho trang booking
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableCustomCode">Kích hoạt mã tùy chỉnh</Label>
                  <p className="text-sm text-gray-500">
                    Cho phép sử dụng mã CSS, JavaScript và HTML tùy chỉnh
                  </p>
                </div>
                <Switch
                  id="enableCustomCode"
                  checked={customizationSettings.enableCustomCode}
                  onCheckedChange={checked => handleCustomizationChange('enableCustomCode', checked)}
                />
              </div>

              {customizationSettings.enableCustomCode && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="customCss">CSS tùy chỉnh</Label>
                    <Textarea
                      id="customCss"
                      placeholder=".my-class { color: red; }"
                      value={customizationSettings.customCss}
                      onChange={e => handleCustomizationChange('customCss', e.target.value)}
                      rows={5}
                      className="font-mono text-sm"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customJs">JavaScript tùy chỉnh</Label>
                    <Textarea
                      id="customJs"
                      placeholder="document.addEventListener('DOMContentLoaded', function() { ... });"
                      value={customizationSettings.customJs}
                      onChange={e => handleCustomizationChange('customJs', e.target.value)}
                      rows={5}
                      className="font-mono text-sm"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customHtml">HTML tùy chỉnh (footer)</Label>
                    <Textarea
                      id="customHtml"
                      placeholder="<div class='custom-footer'>...</div>"
                      value={customizationSettings.customHtml}
                      onChange={e => handleCustomizationChange('customHtml', e.target.value)}
                      rows={5}
                      className="font-mono text-sm"
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default BookingPageAdvancedSettings
