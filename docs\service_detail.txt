<PERSON><PERSON><PERSON> hình Chi tiết dịch vụ (Service Detail)
Mục đích: <PERSON><PERSON> cấp thông tin chi tiết và cho phép đặt chỗ.
Bố cục:
Hình ảnh: Carousel ảnh (sân, không gian nhà hàng).
Thông tin chính:
Tên địa điểm.
Địa chỉ (link Google Maps).
Đánh giá + mô tả ngắn.
Lịch đặt chỗ:
<PERSON><PERSON><PERSON> (datepicker đơn giản).
Khung giờ trống (ví dụ: 10:00 - 11:00, 11:00 - 12:00) dưới dạng nút chọn.
Giá: Hiển thị rõ ràng (ví dụ: "150k/giờ" hoặc "200k/combo ăn uống").
Nút CTA: "Đặt chỗ ngay" (to, màu cam).
Gợi ý: Dùng Firebase để đồng bộ khung giờ trống theo thời gian thực.