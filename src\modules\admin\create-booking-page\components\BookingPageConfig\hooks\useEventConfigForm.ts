import type { EventConfigFormValues } from '../schema/eventConfigSchema'
import { zodResolver } from '@hookform/resolvers/zod'
import debounce from 'lodash/debounce'
import { useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { eventConfigSchema } from '../schema/eventConfigSchema'

type UseEventConfigFormProps = {
  defaultValues?: Partial<EventConfigFormValues>
  onSubmit?: (values: EventConfigFormValues) => void
}

export const useEventConfigForm = ({ defaultValues, onSubmit }: UseEventConfigFormProps) => {
  // Create a ref for the debounced function to persist between renders
  const debouncedUpdateRef = useRef(
    debounce((values: EventConfigFormValues) => {
      // setDataConfig(values)
      // eslint-disable-next-line no-console
      console.log(values)
    }, 500),
  )

  // Initialize the form with default values
  const form = useForm<EventConfigFormValues>({
    resolver: zodResolver(eventConfigSchema),
    defaultValues,
    mode: 'onChange', // Enable validation on change
  })

  // Update the store whenever form values change and are valid
  useEffect(() => {
    // Store a reference to the current debounced function
    const debouncedFn = debouncedUpdateRef.current

    const subscription = form.watch((value, { type }) => {
      if (type === 'change' && Object.keys(value).length === Object.keys(eventConfigSchema.shape).length) {
        form.trigger().then((isValid) => {
          if (isValid) {
            debouncedFn(form.getValues())
          }
        })
      }
    })

    return () => {
      subscription.unsubscribe()
      debouncedFn.cancel()
    }
  }, [form])

  // Handle form submission
  const handleSubmit = (values: EventConfigFormValues) => {
    // Update the store with the final values
    // setDataConfig(values)

    if (onSubmit) {
      onSubmit(values)
    } else {
      // If no onSubmit prop is provided, just show a toast
      toast.success('Cấu hình sự kiện đã được lưu', {
        description: 'Các thay đổi đã được áp dụng cho trang đặt vé sự kiện.',
      })
    }
  }

  return {
    form,
    handleSubmit,
  }
}
