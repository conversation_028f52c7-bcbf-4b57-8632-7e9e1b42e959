import LoginScreen from '@/modules/auth/screens/LoginScreen'
import { getTranslations, setRequestLocale } from 'next-intl/server'

type ISignUpPageProps = {
  params: Promise<{ locale: string }>
}

export async function generateMetadata(props: ISignUpPageProps) {
  const { locale } = await props.params
  const t = await getTranslations({
    locale,
    namespace: 'SignIn',
  })

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  }
}

export default async function SignInPage(props: ISignUpPageProps) {
  const { locale } = await props.params
  setRequestLocale(locale)

  return (
    <LoginScreen />
  )
}
