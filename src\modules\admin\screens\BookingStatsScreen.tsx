'use client'

import type { StatsTimeframe } from '@/modules/admin/utils/booking-stats'
import { BookingBarChart } from '@/modules/admin/components/BookingBarChart'
import { BookingLineChart } from '@/modules/admin/components/BookingLineChart'
import { BookingPieChart } from '@/modules/admin/components/BookingPieChart'
import { StatsSelector } from '@/modules/admin/components/StatsSelector'
import { StatsSummaryCard } from '@/modules/admin/components/StatsSummaryCard'
import { BOOKING_STATUSES, MOCK_BOOKINGS } from '@/modules/admin/constants/booking-mock-data'
import {
  getBookingPageStats,
  getMonthlyStats,
  getStatusStats,
  getWeeklyStats,
} from '@/modules/admin/utils/booking-stats'
import React, { useState } from 'react'

const BookingStatsScreen = () => {
  const [timeframe, setTimeframe] = useState<StatsTimeframe>('week')
  const [chartType, setChartType] = useState<'bar' | 'line'>('bar')

  // Lấy dữ liệu thống kê
  const weeklyStats = getWeeklyStats(MOCK_BOOKINGS)
  const monthlyStats = getMonthlyStats(MOCK_BOOKINGS)
  const statusStats = getStatusStats(MOCK_BOOKINGS)
  const pageStats = getBookingPageStats(MOCK_BOOKINGS)

  // Chọn dữ liệu hiển thị dựa trên timeframe
  const timeframeData = timeframe === 'week' ? weeklyStats : monthlyStats

  // Dữ liệu cho biểu đồ tròn
  const pieChartData = Object.entries(BOOKING_STATUSES).map(([key, { label, color }]) => ({
    name: label,
    value: statusStats[key as keyof typeof statusStats] || 0,
    color: `#${color === 'green' ? '4caf50' : color === 'yellow' ? 'ffc107' : color === 'red' ? 'f44336' : color === 'blue' ? '2196f3' : '9e9e9e'}`,
  }))

  return (
    <div className="max-w-7xl mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Thống kê Booking</h1>
        <StatsSelector
          timeframe={timeframe}
          chartType={chartType}
          onTimeframeChange={setTimeframe}
          onChartTypeChange={setChartType}
        />
      </div>

      {/* Thẻ tóm tắt */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <StatsSummaryCard
          title="Tổng số booking"
          value={statusStats.total}
          description="Tổng số lượt đặt lịch"
          change={12}
        />
        <StatsSummaryCard
          title="Đã xác nhận"
          value={statusStats.confirmed}
          description="Số lượng booking đã xác nhận"
          change={8}
          status="confirmed"
        />
        <StatsSummaryCard
          title="Chờ xác nhận"
          value={statusStats.pending}
          description="Số lượng booking đang chờ xác nhận"
          change={-5}
          status="pending"
        />
        <StatsSummaryCard
          title="Đã hoàn thành"
          value={statusStats.completed}
          description="Số lượng booking đã hoàn thành"
          change={15}
          status="completed"
        />
      </div>

      {/* Biểu đồ chính */}
      <div className="mb-8">
        {chartType === 'bar'
          ? (
              <BookingBarChart
                data={timeframeData}
                title={`Số lượng booking ${timeframe === 'week' ? 'theo tuần' : 'theo tháng'}`}
                description={`Thống kê số lượng booking ${timeframe === 'week' ? '8 tuần' : '6 tháng'} gần đây`}
              />
            )
          : (
              <BookingLineChart
                data={timeframeData}
                title={`Xu hướng booking ${timeframe === 'week' ? 'theo tuần' : 'theo tháng'}`}
                description={`Thống kê xu hướng booking ${timeframe === 'week' ? '8 tuần' : '6 tháng'} gần đây`}
              />
            )}
      </div>

      {/* Biểu đồ phụ */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BookingPieChart
          data={pieChartData}
          title="Phân bố theo trạng thái"
          description="Tỷ lệ booking theo trạng thái"
        />

        <BookingBarChart
          data={pageStats.slice(0, 5).map(item => ({
            name: item.name,
            total: item.value,
            confirmed: 0,
            pending: 0,
            rejected: 0,
            cancelled: 0,
            completed: 0,
          }))}
          title="Top 5 dịch vụ được đặt nhiều nhất"
          description="Số lượng booking theo dịch vụ"
          showLegend={false}
        />
      </div>
    </div>
  )
}

export default BookingStatsScreen
