import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import React, { useState } from 'react'

// Mock: Lưu trạng thái notification vào local state (sau này thay bằng API/backend)
export default function NotificationSettingsScreen() {
  const [enabled, setEnabled] = useState(true)
  const [channels, setChannels] = useState({
    email: true,
    dashboard: true,
    socket: false,
  })

  const handleChannelChange = (channel: keyof typeof channels) => {
    setChannels(prev => ({ ...prev, [channel]: !prev[channel] }))
  }

  return (
    <div className="max-w-lg mx-auto p-6">
      <h1 className="text-xl font-bold mb-2">Cài đặt thông báo</h1>
      <p className="text-gray-600 mb-6">Chọn cách bạn muốn nhận thông báo khi có booking mới.</p>

      <div className="flex items-center gap-3 mb-4">
        <Switch checked={enabled} onCheckedChange={setEnabled} id="notif-switch" />
        <label htmlFor="notif-switch" className="text-base font-medium">Nhận thông báo booking mới</label>
      </div>

      {enabled && (
        <div className="space-y-3 ml-2">
          <div className="flex items-center gap-2">
            <Checkbox id="notif-email" checked={channels.email} onCheckedChange={() => handleChannelChange('email')} />
            <label htmlFor="notif-email" className="text-sm">Email</label>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox id="notif-dashboard" checked={channels.dashboard} onCheckedChange={() => handleChannelChange('dashboard')} />
            <label htmlFor="notif-dashboard" className="text-sm">Thông báo trên dashboard</label>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox id="notif-socket" checked={channels.socket} onCheckedChange={() => handleChannelChange('socket')} />
            <label htmlFor="notif-socket" className="text-sm">Thông báo realtime (socket)</label>
          </div>
        </div>
      )}
    </div>
  )
}
