import type { Booking } from '@/modules/admin/constants/booking-mock-data'
import { addMonths, addWeeks, format, isSameMonth, isSameWeek, startOfMonth, startOfWeek } from 'date-fns'
import { vi } from 'date-fns/locale'

export type BookingStatsItem = {
  name: string
  total: number
  confirmed: number
  pending: number
  rejected: number
  cancelled: number
  completed: number
}

export type StatsTimeframe = 'week' | 'month'

// Tạo dữ liệu thống kê theo tuần
export const getWeeklyStats = (bookings: Booking[], weeksCount = 8): BookingStatsItem[] => {
  const now = new Date()
  const stats: BookingStatsItem[] = []

  // Tạo mảng các tuần gần đây
  for (let i = 0; i < weeksCount; i++) {
    const weekStart = startOfWeek(addWeeks(now, -i), { locale: vi })
    const weekLabel = format(weekStart, 'dd/MM', { locale: vi })

    stats.push({
      name: weekLabel,
      total: 0,
      confirmed: 0,
      pending: 0,
      rejected: 0,
      cancelled: 0,
      completed: 0,
    })
  }

  // Tính toán số lượng booking cho mỗi tuần
  bookings.forEach((booking) => {
    const bookingDate = new Date(booking.bookingTime)

    for (let i = 0; i < weeksCount; i++) {
      const weekStart = startOfWeek(addWeeks(now, -i), { locale: vi })

      if (isSameWeek(bookingDate, weekStart, { locale: vi })) {
        const item = stats[i]
        if (item) {
          item.total += 1

          // Tăng số lượng theo trạng thái
          switch (booking.status) {
            case 'confirmed':
              item.confirmed += 1
              break
            case 'pending':
              item.pending += 1
              break
            case 'rejected':
              item.rejected += 1
              break
            case 'cancelled':
              item.cancelled += 1
              break
            case 'completed':
              item.completed += 1
              break
          }
        }

        break // Thoát vòng lặp sau khi đã tìm thấy tuần phù hợp
      }
    }
  })

  // Đảo ngược mảng để hiển thị từ tuần cũ đến mới
  return stats.reverse()
}

// Tạo dữ liệu thống kê theo tháng
export const getMonthlyStats = (bookings: Booking[], monthsCount = 6): BookingStatsItem[] => {
  const now = new Date()
  const stats: BookingStatsItem[] = []

  // Tạo mảng các tháng gần đây
  for (let i = 0; i < monthsCount; i++) {
    const monthStart = startOfMonth(addMonths(now, -i))
    const monthLabel = format(monthStart, 'MM/yyyy', { locale: vi })

    stats.push({
      name: monthLabel,
      total: 0,
      confirmed: 0,
      pending: 0,
      rejected: 0,
      cancelled: 0,
      completed: 0,
    })
  }

  // Tính toán số lượng booking cho mỗi tháng
  bookings.forEach((booking) => {
    const bookingDate = new Date(booking.bookingTime)

    for (let i = 0; i < monthsCount; i++) {
      const monthStart = startOfMonth(addMonths(now, -i))

      if (isSameMonth(bookingDate, monthStart)) {
        const item = stats[i]

        if (item) {
          item.total += 1

          // Tăng số lượng theo trạng thái
          switch (booking.status) {
            case 'confirmed':
              item.confirmed += 1
              break
            case 'pending':
              item.pending += 1
              break
            case 'rejected':
              item.rejected += 1
              break
            case 'cancelled':
              item.cancelled += 1
              break
            case 'completed':
              item.completed += 1
              break
          }

          break // Thoát vòng lặp sau khi đã tìm thấy tháng phù hợp
        }
      }
    }
  })

  // Đảo ngược mảng để hiển thị từ tháng cũ đến mới
  return stats.reverse()
}

// Tạo dữ liệu thống kê tổng hợp theo trạng thái
export const getStatusStats = (bookings: Booking[]) => {
  const stats = {
    total: bookings.length,
    confirmed: 0,
    pending: 0,
    rejected: 0,
    cancelled: 0,
    completed: 0,
  }

  bookings.forEach((booking) => {
    switch (booking.status) {
      case 'confirmed':
        stats.confirmed += 1
        break
      case 'pending':
        stats.pending += 1
        break
      case 'rejected':
        stats.rejected += 1
        break
      case 'cancelled':
        stats.cancelled += 1
        break
      case 'completed':
        stats.completed += 1
        break
    }
  })

  return stats
}

// Tạo dữ liệu thống kê theo booking page
export const getBookingPageStats = (bookings: Booking[]) => {
  const pageStats: Record<string, number> = {}

  bookings.forEach((booking) => {
    if (!pageStats[booking.pageId]) {
      pageStats[booking.pageId] = 0
    }

    // @ts-ignore
    pageStats[booking.pageId] += 1
  })

  // Chuyển đổi thành mảng để dễ sử dụng với biểu đồ
  const result = Object.entries(pageStats).map(([pageId, count]) => {
    const page = bookings.find(b => b.pageId === pageId)
    return {
      id: pageId,
      name: page?.pageName || 'Unknown',
      value: count,
    }
  })

  // Sắp xếp theo số lượng booking giảm dần
  return result.sort((a, b) => b.value - a.value)
}
