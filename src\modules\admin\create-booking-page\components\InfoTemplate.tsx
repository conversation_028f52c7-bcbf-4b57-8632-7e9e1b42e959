import { Card } from '@/components/ui/card'
import Image from 'next/image'
import React from 'react'

type InfoTemplateProps = {
  selected: string
  templateImage: string
  templateName: string
  templateDesc?: string
}

const InfoTemplate: React.FC<InfoTemplateProps> = ({
  selected,
  templateImage,
  templateName,
  templateDesc,
}) => {
  // Hiển thị thông tin bổ sung dựa trên loại template
  const renderTemplateSpecificInfo = () => {
    switch (selected) {
      case 'sport-field-continuous':
        return (
          <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
            Template này cho phép đặt sân với thời gian liên tục, phù hợp cho các sân bóng đá, tennis, cầu lông...
          </div>
        )
      case 'sport-field-slots':
        return (
          <div className="mt-2 text-sm text-green-600 bg-green-50 p-2 rounded">
            Template này cho phép đặt sân theo khung gi<PERSON> cố định (15", 30", 60"), phù hợp cho các phòng gym, yoga...
          </div>
        )
      case 'simple-calendar':
        return (
          <div className="mt-2 text-sm text-purple-600 bg-purple-50 p-2 rounded">
            Template đơn giản chỉ cần chọn ngày, phù hợp cho dịch vụ tư vấn, khám bệnh...
          </div>
        )
      case 'event-booking':
        return (
          <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
            <div className="font-medium mb-1">Template đặt chỗ sự kiện (EVENT_BOOKING)</div>
            <div>Hỗ trợ đặt chỗ cho sự kiện, số người tham gia. Phù hợp cho rạp chiếu phim, nhà hát, hội thảo...</div>
            <div className="mt-1 text-xs">
              Theme:
              <span className="font-mono">LIGHT-1</span>
            </div>
          </div>
        )
      case 'car-booking':
        return (
          <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
            Template đặt xe với vị trí xe, phù hợp cho dịch vụ taxi, thuê xe...
          </div>
        )
      default:
        return null
    }
  }

  return (
    <Card className="p-4 bg-blue-50 border-blue-200">
      <div className="flex items-center">
        <div className="mr-4 bg-white p-1 rounded border shadow-sm">
          <div className="w-16 h-12 relative">
            <Image
              src={templateImage}
              alt={templateName}
              fill
              className="object-cover rounded"
            />
          </div>
        </div>
        <div>
          <div className="font-semibold">
            Template đang chọn:
            {' '}
            {templateName}
          </div>
          <div className="text-sm text-gray-600">{templateDesc}</div>
        </div>
      </div>
      {renderTemplateSpecificInfo()}
    </Card>
  )
}

export default InfoTemplate
