import type { Field } from './types'

// Default values
export const DEFAULT_DISABLED_DATES: string[] = []
export const DEFAULT_HIGHLIGHTED_DATES: Array<{ date: string, color?: string, tooltip?: string }> = []
export const DEFAULT_BUSINESS_HOURS = { start: '08:00', end: '20:00', daysOfWeek: [1, 2, 3, 4, 5, 6] }
export const DEFAULT_FIELDS: Field[] = []

// Default sample fields
export const SAMPLE_FIELDS: Field[] = [
  {
    id: 'field-1',
    name: 'Sân 1',
    type: 'football',
    capacity: 10,
    pricePerHour: 300000,
  },
  {
    id: 'field-2',
    name: 'Sân 2',
    type: 'football',
    capacity: 14,
    pricePerHour: 400000,
  },
  {
    id: 'field-3',
    name: 'Sân 3',
    type: 'football',
    capacity: 10,
    pricePerHour: 350000,
  },
  {
    id: 'field-4',
    name: 'Sân 4',
    type: 'tennis',
    capacity: 4,
    pricePerHour: 250000,
  },
]
