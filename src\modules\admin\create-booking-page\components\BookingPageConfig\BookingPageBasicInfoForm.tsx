import React from 'react'
import { useBookingPageConfigStore } from '../../stores/booking-page-config'
import BlockBasedTemplateConfig from './BlockBasedTemplateConfig'

const BookingPageBasicInfoForm: React.FC = () => {
  const { templateSelected } = useBookingPageConfigStore()

  const renderConfig = () => {
    switch (templateSelected?.templateCode) {
      case 'EVENT_BOOKING':
        return <BlockBasedTemplateConfig />

      default:
        return <></>
    }
  }

  return (
    <>
      <div>
        {renderConfig()}
      </div>
    </>
  )
}

export default BookingPageBasicInfoForm
