'use client'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useRouter } from '@/libs/i18nNavigation'
import { AlertCircle } from 'lucide-react'
import React from 'react'

export default function BookingPageError({
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-primary/5 p-4 sm:p-8">
      <Card className="backdrop-blur-sm bg-background/95 shadow-xl border-primary/10 p-6 sm:p-8 max-w-md w-full">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="bg-red-100 p-3 rounded-full">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold">Booking Page Not Found</h1>
          <p className="text-muted-foreground">
            The booking page you are looking for does not exist or has been removed.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 pt-4 w-full">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => reset()}
            >
              Try Again
            </Button>
            <Button
              className="flex-1"
              onClick={() => router.push('/')}
            >
              Go Home
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
