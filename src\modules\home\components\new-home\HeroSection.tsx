'use client'

import { SvgsAssets } from '@/assets/svgs'
import { Button } from '@/components/ui/button'
import { Env } from '@/libs/Env'
import { useRouter } from '@/libs/i18nNavigation'
import { appPaths } from '@/utils/app-routes'
import { gsap } from 'gsap'
import React, { useEffect, useRef, useState } from 'react'
import { FaArrowRight, FaCheckCircle, FaClock, FaMapMarkerAlt, FaMobileAlt, FaRegCalendarCheck, FaUsers } from 'react-icons/fa'

export const HeroSection = () => {
  const router = useRouter()
  const sectionRef = useRef<HTMLDivElement>(null)
  const headingRef = useRef<HTMLHeadingElement>(null)
  const subheadingRef = useRef<HTMLParagraphElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)
  const demoRef = useRef<HTMLDivElement>(null)

  // Demo state
  const [demoStep, setDemoStep] = useState(0)
  const demoSteps = ['select-template', 'configure', 'preview', 'publish']

  // Auto advance demo
  useEffect(() => {
    const interval = setInterval(() => {
      setDemoStep(prev => (prev + 1) % demoSteps.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [demoSteps.length])

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Create a timeline for the hero section
      const tl = gsap.timeline({ defaults: { ease: 'power3.out' } })

      // Animate the logo and heading
      tl.from('.logo-icon', {
        y: -50,
        opacity: 0,
        duration: 1,
        rotation: -180,
      })
        .from(headingRef.current, {
          y: 50,
          opacity: 0,
          duration: 1,
          stagger: 0.2,
        }, '-=0.5')
        .from(subheadingRef.current, {
          y: 30,
          opacity: 0,
          duration: 0.4,
        }, '-=0.3')
        .from('.highlight-text', {
          backgroundSize: '0% 100%',
          duration: 1,
        }, '-=0.5')
        .from(ctaRef.current, {
          y: 20,
          opacity: 0,
          duration: 0.8,
        }, '-=0.3')
        .from('.demo-container', {
          y: 100,
          opacity: 0,
          duration: 0.8,
        }, '-=0.8')
        .from('.feature-card', {
          scale: 0,
          opacity: 0,
          stagger: 0.1,
          duration: 0.6,
          ease: 'back.out(1.7)',
        }, '-=0.8')
        .from('.floating-element', {
          scale: 0,
          opacity: 0,
          stagger: 0.1,
          duration: 0.6,
          ease: 'back.out(1.7)',
        }, '-=0.8')

      // Create floating animation for decorative elements
      gsap.to('.floating-element', {
        y: '20px',
        duration: 1,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        stagger: {
          each: 0.2,
          from: 'random',
        },
      })

      // Animate the demo steps
      gsap.to('.progress-bar-inner', {
        width: '100%',
        duration: 3,
        repeat: -1,
        ease: 'linear',
      })

      // Animate feature cards
      gsap.to('.feature-card', {
        y: '10px',
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        stagger: {
          each: 0.3,
          from: 'start',
        },
      })

      // Parallax effect on scroll
      gsap.to('.demo-container', {
        y: 50,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top top',
          end: 'bottom top',
          scrub: true,
        },
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [sectionRef])

  const handleGetStarted = () => {
    router.push(appPaths.auth.register())
  }

  return (
    <section
      ref={sectionRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-b from-white to-orange-50 pt-16 pb-32"
    >
      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="floating-element absolute top-1/4 left-1/5 w-12 h-12 bg-orange-200 rounded-full opacity-30" />
        <div className="floating-element absolute top-1/3 right-1/4 w-20 h-20 bg-blue-200 rounded-full opacity-20" />
        <div className="floating-element absolute bottom-1/4 left-1/3 w-16 h-16 bg-green-200 rounded-full opacity-25" />
        <div className="floating-element absolute top-2/3 right-1/5 w-14 h-14 bg-purple-200 rounded-full opacity-20" />
      </div>

      <div className="container mx-auto px-4 z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <div className="flex-1 text-center lg:text-left max-w-2xl">
            <div className="flex items-center justify-center lg:justify-start gap-4 mb-6">
              <SvgsAssets.Logo width={50} height={50} />
              <h1 ref={headingRef} className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
                PickSlot
              </h1>
            </div>

            <p ref={subheadingRef} className="text-xl md:text-2xl text-gray-700 mb-6">
              Tạo
              {' '}
              <span className="highlight-text font-semibold bg-gradient-to-r from-orange-200 to-orange-300 bg-[length:100%_40%] bg-bottom bg-no-repeat">trang đặt chỗ</span>
              {' '}
              chuyên nghiệp chỉ trong vài phút
            </p>

            <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mt-8">
              <Button
                onClick={handleGetStarted}
                size="lg"
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-8 py-6 rounded-full text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                Bắt đầu ngay
                <FaArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>

          <div ref={demoRef} className="demo-container flex-1 relative">
            <div className="relative z-10 bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100 transform rotate-1 hover:rotate-0 transition-transform duration-500">
              {/* Browser mockup header */}
              <div className="bg-gray-100 h-8 flex items-center px-4 space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-400"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                <div className="ml-4 bg-white rounded-md flex-1 h-5 px-2 text-xs flex items-center text-gray-500">pickslot.app/create-booking</div>
              </div>

              {/* Interactive demo content */}
              <div className="p-4 h-[400px] overflow-hidden">
                {/* Progress bar */}
                <div className="w-full h-2 bg-gray-100 rounded-full mb-6 overflow-hidden">
                  <div className="progress-bar-inner h-full bg-gradient-to-r from-orange-400 to-orange-600 w-0"></div>
                </div>

                {/* Demo steps */}
                <div className="flex justify-between mb-6">
                  {demoSteps.map((step, index) => (
                    <div
                      key={step}
                      className={`flex flex-col items-center ${index === demoStep ? 'text-orange-500 scale-110' : 'text-gray-400'} transition-all duration-300`}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${index === demoStep ? 'bg-orange-100 text-orange-500' : 'bg-gray-100 text-gray-400'}`}>
                        {index + 1}
                      </div>
                      <span className="text-xs font-medium">
                        {step === 'select-template' && 'Chọn mẫu'}
                        {step === 'configure' && 'Cấu hình'}
                        {step === 'preview' && 'Xem trước'}
                        {step === 'publish' && 'Xuất bản'}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Demo content area */}
                <div className="bg-gray-50 rounded-xl p-4 h-[280px] relative overflow-hidden">
                  {/* Template selection */}
                  <div className={`absolute inset-0 p-4 transition-all duration-500 ${demoStep === 0 ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-full'}`}>
                    <h3 className="text-lg font-bold mb-3">Chọn mẫu giao diện</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {[1, 2, 3, 4].map(i => (
                        <div key={i} className="feature-card bg-white rounded-lg p-3 border border-gray-200 hover:border-orange-300 cursor-pointer transition-all">
                          <div className="h-16 bg-gradient-to-r from-orange-100 to-orange-200 rounded mb-2"></div>
                          <div className="h-3 w-20 bg-gray-200 rounded mb-1"></div>
                          <div className="h-2 w-16 bg-gray-100 rounded"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Configuration */}
                  <div className={`absolute inset-0 p-4 transition-all duration-500 ${demoStep === 1 ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'}`}>
                    <h3 className="text-lg font-bold mb-3">Cấu hình trang đặt chỗ</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <div className="w-24 h-4 bg-gray-200 rounded mr-3"></div>
                        <div className="flex-1 h-8 bg-white border border-gray-200 rounded"></div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-24 h-4 bg-gray-200 rounded mr-3"></div>
                        <div className="flex-1 h-8 bg-white border border-gray-200 rounded"></div>
                      </div>
                      <div className="flex items-start">
                        <div className="w-24 h-4 bg-gray-200 rounded mr-3 mt-2"></div>
                        <div className="flex-1 h-20 bg-white border border-gray-200 rounded"></div>
                      </div>
                      <div className="flex items-center">
                        <div className="w-24 h-4 bg-gray-200 rounded mr-3"></div>
                        <div className="flex gap-2">
                          <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-500">
                            <FaClock className="text-xs" />
                          </div>
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-500">
                            <FaMapMarkerAlt className="text-xs" />
                          </div>
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-500">
                            <FaUsers className="text-xs" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Preview */}
                  <div className={`absolute inset-0 p-4 transition-all duration-500 ${demoStep === 2 ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'}`}>
                    <h3 className="text-lg font-bold mb-3">Xem trước trang đặt chỗ</h3>
                    <div className="bg-white rounded-lg p-3 h-[220px] flex flex-col">
                      <div className="h-6 w-32 bg-orange-100 rounded mb-3"></div>
                      <div className="h-3 w-48 bg-gray-100 rounded mb-4"></div>
                      <div className="flex-1 grid grid-cols-3 gap-2">
                        <div className="bg-gray-50 rounded p-2 flex flex-col items-center justify-center">
                          <div className="w-8 h-8 rounded-full bg-orange-100 mb-1 flex items-center justify-center">
                            <FaRegCalendarCheck className="text-orange-500 text-xs" />
                          </div>
                          <div className="h-2 w-12 bg-gray-200 rounded"></div>
                        </div>
                        <div className="bg-gray-50 rounded p-2 flex flex-col items-center justify-center">
                          <div className="w-8 h-8 rounded-full bg-blue-100 mb-1 flex items-center justify-center">
                            <FaClock className="text-blue-500 text-xs" />
                          </div>
                          <div className="h-2 w-12 bg-gray-200 rounded"></div>
                        </div>
                        <div className="bg-gray-50 rounded p-2 flex flex-col items-center justify-center">
                          <div className="w-8 h-8 rounded-full bg-green-100 mb-1 flex items-center justify-center">
                            <FaUsers className="text-green-500 text-xs" />
                          </div>
                          <div className="h-2 w-12 bg-gray-200 rounded"></div>
                        </div>
                      </div>
                      <div className="mt-4 h-8 w-32 bg-orange-400 rounded-full self-center"></div>
                    </div>
                  </div>

                  {/* Publish */}
                  <div className={`absolute inset-0 p-4 transition-all duration-500 ${demoStep === 3 ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'}`}>
                    <h3 className="text-lg font-bold mb-3">Xuất bản & Chia sẻ</h3>
                    <div className="bg-white rounded-lg p-4 flex flex-col items-center justify-center h-[220px]">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center text-green-500 mb-4">
                        <FaCheckCircle className="text-2xl" />
                      </div>
                      <div className="h-4 w-48 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 w-32 bg-gray-100 rounded mb-4"></div>
                      <div className="flex gap-2 mb-4">
                        <div className="h-8 w-8 bg-blue-100 rounded flex items-center justify-center text-blue-500">
                          <FaMobileAlt className="text-sm" />
                        </div>
                        <div className="h-8 w-64 bg-gray-100 rounded flex items-center px-2 text-xs text-gray-500 overflow-hidden">
                          {Env.NEXT_PUBLIC_DOMAIN}
                          /your-page
                        </div>
                        <div className="h-8 w-8 bg-orange-100 rounded flex items-center justify-center text-orange-500">
                          <FaArrowRight className="text-sm" />
                        </div>
                      </div>
                      <div className="h-8 w-32 bg-orange-400 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating elements around the demo */}
            <div className="floating-element absolute -top-6 -right-6 bg-orange-500 text-white p-3 rounded-lg shadow-lg transform rotate-3 z-10">
              <span className="text-sm font-medium">Dễ dàng tùy chỉnh</span>
            </div>
            <div className="floating-element absolute -bottom-4 -left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg transform -rotate-2 z-10">
              <span className="text-sm font-medium">Tăng khả năng tiếp cận khách hàng</span>
            </div>
            <div className="floating-element absolute top-1/2 -right-4 bg-green-500 text-white p-3 rounded-lg shadow-lg transform rotate-1 z-10">
              <span className="text-sm font-medium">Quản lý đặt chỗ hiệu quả</span>
            </div>
            <div className="floating-element absolute top-1 -left-4 bg-purple-500 text-white p-3 rounded-lg shadow-lg transform -rotate-1 z-10">
              <span className="text-sm font-medium">Tích hợp thanh toán</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
