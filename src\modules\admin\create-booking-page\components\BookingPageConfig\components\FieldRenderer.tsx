import type { UseFormReturn } from 'react-hook-form'
import type { FieldConfig } from '../config/eventFieldsConfig'
import type { EventConfigFormValues } from '../schema/eventConfigSchema'
import React from 'react'
import { ColorPickerField } from './ColorPickerField'
import { PaymentOptionsField } from './PaymentOptionsField'
import { TextInputField } from './TextInputField'

type FieldRendererProps = {
  field: FieldConfig
  form: UseFormReturn<EventConfigFormValues>
}

/**
 * FieldRenderer Component
 *
 * Renders the appropriate field component based on the field type
 */
export const FieldRenderer: React.FC<FieldRendererProps> = ({ field, form }) => {
  const { type, name, label, description, fullWidth } = field

  switch (type) {
    case 'color':
      return <ColorPickerField form={form} />

    case 'payment-options':
      return <PaymentOptionsField form={form} />

    case 'text':
    case 'number':
      return (
        <TextInputField
          form={form}
          name={name}
          label={label}
          description={description}
          type={type}
          fullWidth={fullWidth}
        />
      )

    default: {
      // For TypeScript exhaustiveness checking
      // @ts-expect-error This is for exhaustiveness checking
      const _exhaustiveCheck: never = type
      return null
    }
  }
}
