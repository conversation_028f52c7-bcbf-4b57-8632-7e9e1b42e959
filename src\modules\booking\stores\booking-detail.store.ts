import type { BookingDetail } from '@/modules/booking/booking.apis'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface BookingDetailState {
  // State
  bookingDetail: BookingDetail | null
  isLoading: boolean
  error: string | null

  // Actions
  setBookingDetail: (bookingDetail: BookingDetail | null) => void
  setLoading: (isLoading: boolean) => void
  setError: (error: string | null) => void
  loadBookingDetail: (id: string) => Promise<void>
  reset: () => void
}

// Create the store with performance optimizations
export const useBookingDetailStore = create<BookingDetailState>()(
  devtools(

    immer((set, get) => ({
      // Initial state
      bookingDetail: null,
      isLoading: false,
      error: null,

      // Actions
      setBookingDetail: bookingDetail =>
        set(
          (state) => {
            state.bookingDetail = bookingDetail
          },
          false,
          'setBookingDetail',
        ),

      setLoading: isLoading =>
        set(
          (state) => {
            state.isLoading = isLoading
          },
          false,
          'setLoading',
        ),

      setError: error =>
        set(
          (state) => {
            state.error = error
          },
          false,
          'setError',
        ),

      loadBookingDetail: async (id: string) => {
        // eslint-disable-next-line no-console
        console.log('loadBookingDetail', get().bookingDetail)
        set(
          (state) => {
            state.isLoading = true
            state.error = null
          },
          false,
          'loadBookingDetail:start',
        )

        try {
          const response = await bookingAPIs.getBookingDetail(id)

          if (response.status?.success && response.data) {
            set(
              (state) => {
                state.bookingDetail = response.data!
                state.isLoading = false
              },
              false,
              'loadBookingDetail:success',
            )
          } else {
            throw new Error(response.status?.message || 'Failed to load booking detail')
          }
        } catch (error) {
          console.error('Error loading booking detail:', error)
          set(
            (state) => {
              state.error = error instanceof Error ? error.message : 'Unknown error'
              state.isLoading = false
              state.bookingDetail = null
            },
            false,
            'loadBookingDetail:error',
          )
        }
      },

      reset: () =>
        set(
          (state) => {
            state.bookingDetail = null
            state.isLoading = false
            state.error = null
          },
          false,
          'reset',
        ),
    })),
    {
      name: 'booking-detail-store',
    },
  ),
)
