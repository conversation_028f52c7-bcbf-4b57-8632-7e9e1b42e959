'use client'

import type { BookingStatsItem } from '@/modules/admin/utils/booking-stats'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { BOOKING_STATUSES } from '@/modules/admin/constants/booking-mock-data'
import React from 'react'
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts'

type BookingLineChartProps = {
  data: BookingStatsItem[]
  title: string
  description?: string
  showLegend?: boolean
}

export const BookingLineChart = ({
  data,
  title,
  description,
  showLegend = true,
}: BookingLineChartProps) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 0,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => {
                  return [`${value} lượt đặt`, name === 'total' ? 'Tổng số' : BOOKING_STATUSES[name as keyof typeof BOOKING_STATUSES]?.label || name]
                }}
              />
              {showLegend && (
                <Legend
                  formatter={(value) => {
                    return value === 'total' ? 'Tổng số' : BOOKING_STATUSES[value as keyof typeof BOOKING_STATUSES]?.label || value
                  }}
                />
              )}
              <Line
                type="monotone"
                dataKey="total"
                stroke="#8884d8"
                activeDot={{ r: 8 }}
                name="total"
                strokeWidth={2}
              />
              <Line type="monotone" dataKey="confirmed" stroke="#82ca9d" name="confirmed" />
              <Line type="monotone" dataKey="pending" stroke="#ffc658" name="pending" />
              <Line type="monotone" dataKey="completed" stroke="#0088FE" name="completed" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
