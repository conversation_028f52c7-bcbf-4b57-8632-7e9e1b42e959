import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import React from 'react'

export default function BookingPageLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/10 via-background to-primary/5 p-4 sm:p-8">
      <div className="w-full max-w-3xl">
        <Card className="backdrop-blur-sm bg-background/95 shadow-xl border-primary/10 p-6 sm:p-8 space-y-8">
          <div className="space-y-3 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Loading Booking Page
            </h1>
            <p className="text-muted-foreground text-sm">Please wait while we prepare your booking page...</p>
          </div>

          <Progress value={75} className="h-2" />

          <div className="space-y-6">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-32 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
            <Skeleton className="h-48 w-full" />
          </div>
        </Card>
      </div>
    </div>
  )
}
