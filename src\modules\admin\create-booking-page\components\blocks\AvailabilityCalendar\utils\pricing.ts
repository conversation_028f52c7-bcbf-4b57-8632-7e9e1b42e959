import type { CombinedPricingRule, DayBasedPrice, Field, TimeBasedPrice } from '../types'

/**
 * Get the price for a specific field at a specific time and date
 * @param field The field to get the price for
 * @param date The date to get the price for
 * @param time The time to get the price for (HH:MM format)
 * @returns The price for the field at the specified time and date
 */
export const getFieldPrice = (field: Field, date: Date, time: string): number => {
  // If dynamic pricing is not enabled, return the base price
  if (!field.dynamicPricing?.enabled) {
    return field.pricePerHour
  }

  // Get the day of the week (0 = Sunday, 1 = Monday, etc.)
  const dayOfWeek = date.getDay()

  // Check if there's a combined rule that matches both day and time
  const combinedRule = findMatchingCombinedRule(field.dynamicPricing.combinedRules || [], dayOfWeek, time)
  if (combinedRule) {
    return combinedRule.price
  }

  // Check if there's a day-based price for this day
  const dayPrice = findMatchingDayPrice(field.dynamicPricing.dayBasedPrices || [], dayOfWeek)
  if (dayPrice) {
    return dayPrice.price
  }

  // Check if there's a time-based price for this time
  const timePrice = findMatchingTimePrice(field.dynamicPricing.timeBasedPrices || [], time)
  if (timePrice) {
    return timePrice.price
  }

  // If no matching rules, return the base price
  return field.pricePerHour
}

/**
 * Find a matching combined rule for a specific day and time
 * @param rules The combined rules to check
 * @param dayOfWeek The day of the week (0 = Sunday, 1 = Monday, etc.)
 * @param time The time to check (HH:MM format)
 * @returns The matching rule or undefined if no match
 */
export const findMatchingCombinedRule = (
  rules: CombinedPricingRule[],
  dayOfWeek: number,
  time: string,
): CombinedPricingRule | undefined => {
  return rules.find((rule) => {
    // Check if the rule applies to this day
    if (!rule.days.includes(dayOfWeek)) {
      return false
    }

    // Check if the time is within the rule's time range
    return isTimeInRange(time, rule.startTime, rule.endTime)
  })
}

/**
 * Find a matching day-based price for a specific day
 * @param prices The day-based prices to check
 * @param dayOfWeek The day of the week (0 = Sunday, 1 = Monday, etc.)
 * @returns The matching price or undefined if no match
 */
export const findMatchingDayPrice = (
  prices: DayBasedPrice[],
  dayOfWeek: number,
): DayBasedPrice | undefined => {
  return prices.find(price => price.day === dayOfWeek)
}

/**
 * Find a matching time-based price for a specific time
 * @param prices The time-based prices to check
 * @param time The time to check (HH:MM format)
 * @returns The matching price or undefined if no match
 */
export const findMatchingTimePrice = (
  prices: TimeBasedPrice[],
  time: string,
): TimeBasedPrice | undefined => {
  return prices.find(price => isTimeInRange(time, price.startTime, price.endTime))
}

/**
 * Check if a time is within a range
 * @param time The time to check (HH:MM format)
 * @param startTime The start time of the range (HH:MM format)
 * @param endTime The end time of the range (HH:MM format)
 * @returns True if the time is within the range, false otherwise
 */
export const isTimeInRange = (time: string, startTime: string, endTime: string): boolean => {
  // Convert times to minutes for easier comparison
  const timeMinutes = convertTimeToMinutes(time)
  const startMinutes = convertTimeToMinutes(startTime)
  const endMinutes = convertTimeToMinutes(endTime)

  // Handle cases where the end time is on the next day
  if (endMinutes < startMinutes) {
    return timeMinutes >= startMinutes || timeMinutes <= endMinutes
  }

  // Normal case
  return timeMinutes >= startMinutes && timeMinutes <= endMinutes
}

/**
 * Convert a time string to minutes
 * @param time The time to convert (HH:MM format)
 * @returns The time in minutes
 */
export const convertTimeToMinutes = (time: string): number => {
  const [hours = 0, minutes = 0] = time.split(':').map(Number)
  return hours * 60 + minutes
}

/**
 * Create example combined rules for weekday and weekend pricing
 * @returns An array of combined pricing rules
 */
export const createExampleCombinedRules = (): CombinedPricingRule[] => {
  return [
    {
      id: `rule-${Date.now()}-1`,
      name: 'Giờ thấp điểm ngày thường',
      days: [1, 2, 3, 4, 5], // Monday to Friday
      startTime: '08:00',
      endTime: '16:00',
      price: 300000,
    },
    {
      id: `rule-${Date.now()}-2`,
      name: 'Giờ cao điểm ngày thường',
      days: [1, 2, 3, 4, 5], // Monday to Friday
      startTime: '17:00',
      endTime: '21:00',
      price: 400000,
    },
    {
      id: `rule-${Date.now()}-3`,
      name: 'Cuối tuần',
      days: [0, 6], // Sunday and Saturday
      startTime: '08:00',
      endTime: '21:00',
      price: 450000,
    },
  ]
}
