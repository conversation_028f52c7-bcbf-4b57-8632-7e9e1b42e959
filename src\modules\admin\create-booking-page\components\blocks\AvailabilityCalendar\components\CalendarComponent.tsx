import type { CalendarComponentProps } from '../types'
import { Calendar } from '@/components/ui/calendar'
import { Label } from '@/components/ui/label'
import { isSameDay } from 'date-fns'
import { vi } from 'date-fns/locale'
import React from 'react'

/**
 * Calendar component for date selection
 */
export const CalendarComponent: React.FC<CalendarComponentProps> = ({
  selectedDate,
  onDateSelect,
  parsedMinDate,
  parsedMaxDate,
  parsedDisabledDates,
  highlightedDates,
  firstDayOfWeek,
}) => {
  return (
    <div className="mb-6">
      <Label className="block text-sm font-medium mb-2">Chọn ngày</Label>
      <div className="border rounded-md p-2 bg-white relative">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={onDateSelect}
          disabled={(date) => {
            // Disable dates outside min/max range
            if (parsedMinDate && date < parsedMinDate) {
              return true
            }
            if (parsedMaxDate && date > parsedMaxDate) {
              return true
            }

            // Disable specific dates
            return parsedDisabledDates.some(disabledDate =>
              isSameDay(date, disabledDate),
            )
          }}
          modifiers={{
            highlighted: date =>
              highlightedDates?.some(h =>
                isSameDay(date, new Date(h.date)),
              ) || false,
          }}
          modifiersClassNames={{
            highlighted: 'bg-blue-100 text-blue-900 font-medium',
          }}
          weekStartsOn={firstDayOfWeek}
          locale={vi}
          className="mx-auto"
        />
      </div>
    </div>
  )
}
