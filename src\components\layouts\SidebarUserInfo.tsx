import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

export type UserInfo = {
  name: string
  avatarUrl: string
}

export function SidebarUserInfo({ user, planId, planName, isSidebarOpen }: { user: UserInfo, planId: string, planName: string, isSidebarOpen: boolean }) {
  return (
    <div className={`flex items-center gap-3 p-6 border-b ${!isSidebarOpen && 'md:justify-center'}`}>
      <div>
        <Avatar className="border-2 border-primary transition-all duration-200 hover:scale-110 hover:shadow-md">
          <AvatarImage src={user.avatarUrl} alt={user.name} />
          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
        </Avatar>
      </div>
      {isSidebarOpen && (
        <div>
          <div className="font-semibold">{user.name}</div>
          <div className="mt-1 text-xs">
            <span className={`px-2 py-0.5 rounded font-semibold ${planId === 'pro' ? 'bg-blue-100 text-blue-700' : planId === 'enterprise' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-200 text-gray-600'}`}>
              {planName}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
