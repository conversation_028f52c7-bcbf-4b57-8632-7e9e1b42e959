import type { Field } from '../AvailabilityCalendar/types'
import type { FieldWithDynamicPricing } from './types'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React, { useCallback } from 'react'

/**
 * Component for individual field configuration
 */
export const IndividualFieldConfig: React.FC<{
  field: Field
  index: number
  onChange: (updatedField: Field) => void
}> = ({ field, index, onChange }) => {
  const fieldWithDynamicPricing = field as FieldWithDynamicPricing

  // Handle field name change
  const handleNameChange = useCallback((name: string) => {
    onChange({
      ...field,
      name,
    })
  }, [field, onChange])

  // Handle price per hour change
  const handlePriceChange = useCallback((price: number) => {
    onChange({
      ...field,
      pricePerHour: price,
    })
  }, [field, onChange])

  // Toggle dynamic pricing
  const handleToggleDynamicPricing = useCallback((enabled: boolean) => {
    onChange({
      ...field,
      dynamicPricing: {
        ...(fieldWithDynamicPricing.dynamicPricing || { timeBasedPrices: [], dayBasedPrices: [], combinedRules: [] }),
        enabled,
      },
    })
  }, [field, fieldWithDynamicPricing, onChange])

  return (
    <div className="border p-4 rounded-md">
      <Label className="text-sm font-medium">
        Sân
        {' '}
        {index + 1}
      </Label>
      <div className="mt-3">
        <div className="grid grid-cols-1 gap-3">
          <div>
            <Label className="text-xs">Tên sân</Label>
            <Input
              type="text"
              value={field.name}
              onChange={e => handleNameChange(e.target.value)}
              className="w-full"
            />
          </div>

          <div>
            <Label className="text-xs">Giá/giờ cơ bản (VNĐ)</Label>
            <Input
              type="number"
              value={field.pricePerHour}
              onChange={e => handlePriceChange(Number(e.target.value))}
              className="w-full"
            />
          </div>

          <div className="border-t pt-3">
            <div className="flex items-center space-x-2 mb-3">
              <Checkbox
                id={`enable-dynamic-pricing-${field.id}`}
                checked={fieldWithDynamicPricing.dynamicPricing?.enabled || false}
                onCheckedChange={checked => handleToggleDynamicPricing(!!checked)}
              />
              <Label htmlFor={`enable-dynamic-pricing-${field.id}`}>
                Sử dụng cấu hình giá từ cấu hình chung
              </Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
