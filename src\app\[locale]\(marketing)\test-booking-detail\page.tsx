'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import BookingDetailPage from '@/modules/booking/components/BookingDetailPage'
import BookingSummaryCard from '@/modules/booking/components/BookingSummaryCard'
import { MOCK_BOOKING_DETAIL, MOCK_BOOKING_DETAIL_CANCELLED, MOCK_BOOKING_DETAIL_PENDING } from '@/modules/booking/mock-data/booking-detail.mock'
import { useState } from 'react'

export default function TestBookingDetailPage() {
  const [selectedMock, setSelectedMock] = useState<'confirmed' | 'pending' | 'cancelled'>('confirmed')

  const getMockData = () => {
    switch (selectedMock) {
      case 'confirmed':
        return MOCK_BOOKING_DETAIL
      case 'pending':
        return MOCK_BOOKING_DETAIL_PENDING
      case 'cancelled':
        return MOCK_BOOKING_DETAIL_CANCELLED
      default:
        return MOCK_BOOKING_DETAIL
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Test Controls */}
      <div className="bg-white border-b p-4">
        <div className="max-w-6xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Test Booking Detail Components</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-4">
                <Button
                  variant={selectedMock === 'confirmed' ? 'default' : 'outline'}
                  onClick={() => setSelectedMock('confirmed')}
                >
                  Confirmed Booking
                </Button>
                <Button
                  variant={selectedMock === 'pending' ? 'default' : 'outline'}
                  onClick={() => setSelectedMock('pending')}
                >
                  Pending Booking
                </Button>
                <Button
                  variant={selectedMock === 'cancelled' ? 'default' : 'outline'}
                  onClick={() => setSelectedMock('cancelled')}
                >
                  Cancelled Booking
                </Button>
              </div>

              <Tabs defaultValue="detail" className="w-full">
                <TabsList>
                  <TabsTrigger value="detail">Detail Page</TabsTrigger>
                  <TabsTrigger value="summary">Summary Card</TabsTrigger>
                  <TabsTrigger value="both">Both</TabsTrigger>
                </TabsList>

                <TabsContent value="detail" className="mt-4">
                  <BookingDetailPage bookingDetail={getMockData()} />
                </TabsContent>

                <TabsContent value="summary" className="mt-4">
                  <div className="flex justify-center">
                    <BookingSummaryCard bookingDetail={getMockData()} />
                  </div>
                </TabsContent>

                <TabsContent value="both" className="mt-4">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div className="lg:col-span-1">
                      <BookingSummaryCard bookingDetail={getMockData()} />
                    </div>
                    <div className="lg:col-span-3">
                      <BookingDetailPage bookingDetail={getMockData()} />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
